.browserWindow {
  border: 3px solid var(--ifm-color-emphasis-200);
  border-top-left-radius: var(--ifm-global-radius);
  border-top-right-radius: var(--ifm-global-radius);
  margin-bottom: 10px;
}

.browserWindowHeader {
  align-items: center;
  background: var(--ifm-color-emphasis-200);
  display: flex;
  padding: 0.5rem 1rem;
}

.row:after {
  content: '';
  display: table;
  clear: both;
}

.buttons {
  white-space: nowrap;
}

.right {
  align-self: center;
  width: 10%;
}

.browserWindowAddressBar {
  flex: 1 0;
  margin: 0 1rem 0 0.5rem;
  border-radius: 12.5px;
  background-color: #fff;
  color: #666;
  padding: 5px 15px;
  font: 400 13px Arial;
  user-select: none;
}

.dot {
  margin-right: 6px;
  margin-top: 4px;
  height: 12px;
  width: 12px;
  background-color: #bbb;
  border-radius: 50%;
  display: inline-block;
}

.browserWindowMenuIcon {
  margin-left: auto;
}

.bar {
  width: 17px;
  height: 3px;
  background-color: #aaa;
  margin: 3px 0;
  display: block;
}

.browserWindowBody {
  padding: 1rem;
}
