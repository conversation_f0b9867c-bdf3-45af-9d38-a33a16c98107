import { cn } from '@site/src/lib/utils'
import { type ComponentProps, type ReactNode } from 'react'
import styles from './styles.module.css'

export interface SvgIconProps extends ComponentProps<'svg'> {
  viewBox?: string
  size?: 'inherit' | 'small' | 'medium' | 'large'
  color?: 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'warning'
  svgClass?: string // Class attribute on the child
  colorAttr?: string // Applies a color attribute to the SVG element.
  children: ReactNode // Node passed into the SVG element.
}

export default function Svg(props: SvgIconProps): JSX.Element {
  const { svgClass, colorAttr, children, color = 'inherit', size = 'medium', viewBox = '0 0 24 24', ...rest } = props

  return (
    <svg
      viewBox={viewBox}
      color={colorAttr}
      aria-hidden
      className={cn(styles.svgIcon, styles[color], styles[size], svgClass)}
      {...rest}
    >
      {children}
    </svg>
  )
}
