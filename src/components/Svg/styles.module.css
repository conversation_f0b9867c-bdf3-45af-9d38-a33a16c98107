.svgIcon {
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  fill: currentColor;
  flex-shrink: 0;
  color: inherit;
}

/* font-size */
.small {
  font-size: 1.25rem;
}

.medium {
  font-size: 1.5rem;
}

.large {
  font-size: 2.185rem;
}

/* colors */
.primary {
  color: var(--ifm-color-primary);
}

.secondary {
  color: var(--ifm-color-secondary);
}

.success {
  color: var(--ifm-color-success);
}

.error {
  color: var(--ifm-color-error);
}

.warning {
  color: var(--ifm-color-warning);
}

.inherit {
  color: inherit;
}
