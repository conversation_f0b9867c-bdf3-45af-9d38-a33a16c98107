/* hero */
.hero {
  @apply h-[calc(100vh-60px)] w-screen max-w-full m-0 grid grid-cols-[8fr_11fr] items-center relative;
  letter-spacing: 0.04em;
  padding: 0;
}

.intro {
  @apply p-4 pl-16 relative z-10;
}

.intro > p {
  @apply my-6 text-[#6e7b8c] text-justify text-base leading-8;
  letter-spacing: -0.04em;
  text-shadow: 0 0 #8c99ab;
}

.hero_text {
  font-size: calc(1.5em + 1.2vw);
}

.name {
  --lighting-size: 300px;
  --lighting-color: var(--ifm-color-primary);
  --lighting-highlight-color: var(--ifm-color-primary-lightest);

  background-image: radial-gradient(
    var(--lighting-highlight-color),
    var(--lighting-color),
    var(--lighting-color)
  );
  background-size: var(--lighting-size) var(--lighting-size);
  background-repeat: no-repeat;
  background-position-x: calc(
    var(--x) - var(--mouse-x) - calc(var(--lighting-size) / 2)
  );
  background-position-y: calc(
    var(--y) - var(--mouse-y) - calc(var(--lighting-size) / 2)
  );
  background-color: var(--lighting-color);
  color: transparent;
  background-clip: text;
}

.background {
  @apply relative w-full h-[90%] grid place-items-center items-start;
  z-index: 5;
}

.background svg {
  @apply w-full h-auto;
}

.circle {
  @apply absolute top-0 w-full h-full bg-gradient-to-r from-[rgb(150,255,244,0.81)] to-[rgb(0,71,252,0.81)] rounded-full opacity-30;
  filter: blur(80px);
  z-index: -1;
}

.box {
  @apply absolute inline-flex justify-center items-center bg-transparent text-transparent backdrop-blur-[2px] shadow-[inset_1px_1px_5px_rgb(255,255,255,0.3),0_0_5px_rgb(0,0,0,0.2)] rounded-lg p-2 w-14 h-14;
}

@media (max-width: 1000px) {
  .hero {
    @apply grid-cols-1 grid-rows-[max-content_minmax(0,max-content)] items-start h-auto;
  }

  .intro {
    @apply p-0 pt-16 flex flex-col items-center;
  }

  .background {
    @apply w-full justify-self-center pt-16 h-full grid place-items-center;
  }

  .background svg {
    @apply w-[90%] h-auto;
  }

  .box {
    @apply w-12 h-12;
  }
}

@media (max-width: 570px) {
  .hero {
    @apply h-auto;
  }

  .background {
    @apply pt-8;
  }

  .background svg {
    @apply w-full h-auto;
  }

  .box {
    @apply w-8 h-8;
  }

  .intro {
    @apply pt-8;
  }
}
