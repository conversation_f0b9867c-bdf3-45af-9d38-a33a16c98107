.archiveTitle {
  display: inline-flex;
  align-items: center;
}

.archiveCount {
  text-align: right;
  margin-top: -2.5rem;
  font-size: 0.85rem;
  opacity: 0.8;
}

.archiveYear {
  position: relative;
  display: inline-flex;
  justify-content: space-between;
  align-items: flex-end;
  margin: 1rem 0;
  width: 100%;
  font-weight: 400;
  font-size: 1.4rem;
}

.archiveYear span {
  font-size: 0.85rem;
  font-weight: 300;
  color: var(--ifm-secondary-text-color);
}

.archiveYearTitle {
  display: inline-flex;
  margin: 0;
  font-size: 1.4rem;
  font-weight: 400;
  margin-right: 0.5rem;
  color: var(--ifm-text-color);
}

.archiveYearTitle::before {
  margin-right: 0.5em;
  width: 0.3em;
  display: inline-block;
  background: var(--ifm-color-primary);
  color: transparent;
  content: '.';
}

.archiveList {
  padding: 0;
  margin: 0;
}

.archiveItem {
  position: relative;
  display: flex;
  list-style: none;
}

.archiveItem::before {
  content: '';
  position: absolute;
  width: 2px;
  top: 0;
  left: 0.1rem;
  height: 100%;
  background: var(--ifm-color-primary-lighter);
}

.archiveItem:first-child::before {
  height: 50%;
  transform: translateY(100%);
}

.archiveItem:last-child::before {
  height: 50%;
}

.archiveItem a {
  display: flex;
  align-items: center;
  color: var(--ifm-text-color);
  padding: 0.2rem 1rem;
  font-size: 1.2rem;
  font-weight: 500;
  font-family: var(--ifm-font-family-base);

  text-decoration: none;
  white-space: nowrap;
  transition: padding 0.3s;
}

.archiveItem a::before {
  content: '';
  position: absolute;
  left: 0.1rem;
  top: 0.9rem;
  width: 0.5rem;
  height: 0.5rem;
  margin-left: -4px;
  border-radius: 50%;
  border: 1px solid var(--ifm-color-primary);
  background-color: rgb(255 255 255);
  z-index: 1;
  transition-duration: 0.3s;
  transition-delay: 0s;
  transition-property: background;
}

.archiveItem a:hover::before {
  background: var(--ifm-color-primary);
}

.archiveTime {
  opacity: 0.6;
  font-size: 0.85rem;
  font-weight: 400;
  margin-right: 0.5rem;
  width: 3rem;
  position: relative;
  color: var(--hty-secondary-text-color);

  font-family: 'Source Code Pro', Consolas, Monaco, SFMono-Regular,
    'Ubuntu Mono', Menlo, monospace;
}

.archiveItem a > span:hover {
  color: var(--ifm-color-primary);
}
