:global(.theme-code-block:hover) .copyButtonCopied {
  opacity: 1 !important;
}

.copyButtonIcons {
  position: relative;
  width: 1.125rem;
  height: 1.125rem;
}

.copyButtonIcon,
.copyButtonSuccessIcon {
  position: absolute;
  top: 0;
  left: 0;
  fill: currentColor;
  opacity: inherit;
  width: inherit;
  height: inherit;
  transition: all var(--ifm-transition-fast) ease;
}

.copyButtonSuccessIcon {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.33);
  opacity: 0;
  color: #00d600;
}

.copyButtonCopied .copyButtonIcon {
  transform: scale(0.33);
  opacity: 0;
}

.copyButtonCopied .copyButtonSuccessIcon {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
  transition-delay: 0.075s;
}
