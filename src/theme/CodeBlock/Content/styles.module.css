.codeBlockContent {
  position: relative;
  /* rtl:ignore */
  direction: ltr;
  border-radius: inherit;
}

.codeBlockTitle {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;

  border-bottom: 1px solid var(--ifm-color-emphasis-300);
  font-size: var(--ifm-code-font-size);
  font-weight: 500;
  padding: 0.75rem var(--ifm-pre-padding);
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}

.codeBlock {
  --ifm-pre-background: var(--prism-background-color);
  padding: 0;
}

.codeBlockTitle + .codeBlockContent .codeBlock {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.codeBlockStandalone {
  padding: 0;
}

.codeBlockLines {
  font: inherit;
  /* rtl:ignore */
  float: left;
  min-width: 100%;
  padding: var(--ifm-pre-padding);
}

.codeBlockLinesWithNumbering {
  display: table;
  padding: var(--ifm-pre-padding) 0;
}

@media print {
  .codeBlockLines {
    white-space: pre-wrap;
  }
}

.buttonGroup {
  display: flex;
  column-gap: 0.2rem;
  position: absolute;
  /* rtl:ignore */
  right: calc(var(--ifm-pre-padding) / 2);
  top: calc(var(--ifm-pre-padding) / 2);
}

.buttonGroup button {
  display: flex;
  align-items: center;
  background: var(--prism-background-color);
  color: var(--prism-color);
  border: 1px solid var(--ifm-color-emphasis-300);
  border-radius: var(--ifm-global-radius);
  padding: 0.4rem;
  line-height: 0;
  transition: opacity var(--ifm-transition-fast) ease-in-out;
  opacity: 0;
}

.buttonGroup button:focus-visible,
.buttonGroup button:hover {
  opacity: 1 !important;
}

:global(.theme-code-block:hover) .buttonGroup button {
  opacity: 0.4;
}
