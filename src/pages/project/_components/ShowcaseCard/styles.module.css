.showcaseCard {
  position: relative;
  box-shadow: var(--blog-item-shadow);
  transition: opacity 0.5s;
  will-change: transform;
  overflow: hidden;
}

.showcaseCard::before {
  display: block;
  font-weight: bold;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}

.showcaseCardImage {
  overflow: hidden;
  height: 150px;
  border-bottom: 2px solid var(--ifm-color-emphasis-200);
}

.showcaseCardHeader {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.showcaseCardTitle {
  margin-bottom: 0;
  flex: 1 1 auto;
}

.showcaseCardTitle a {
  @apply no-underline;
  background: linear-gradient(
      var(--ifm-color-primary),
      var(--ifm-color-primary)
    )
    no-repeat right bottom;
  background-size: 0px 2px;
  transition: background-size 300ms;
}

.showcaseCardTitle a:hover {
  background-size: 100% 2px;
  background-position: left bottom;
}

.showcaseCardTitle,
.showcaseCardHeader .svgIconFavorite {
  margin-right: 0.25rem;
}

.showcaseCardHeader .svgIconFavorite {
  color: var(--site-color-svg-icon-favorite);
}

.showcaseCardSrcBtn {
  margin-left: 6px;
  padding-left: 12px;
  padding-right: 12px;
  border: none;
}

.showcaseCardSrcBtn:focus-visible {
  background-color: var(--ifm-color-secondary-dark);
}

html[data-theme='dark'] .showcaseCardSrcBtn {
  background-color: var(--ifm-color-emphasis-200) !important;
  color: inherit;
}

html[data-theme='dark'] .showcaseCardSrcBtn:hover {
  background-color: var(--ifm-color-emphasis-300) !important;
}

.showcaseCardBody {
  font-size: smaller;
  line-height: 1.66;
}

.cardFooter {
  display: flex;
  flex-wrap: wrap;

  margin: 0;
}

.tag {
  font-size: 0.675rem;
  border: 1px solid var(--ifm-color-secondary-darkest);
  cursor: default;
  margin-right: 6px;
  margin-bottom: 6px !important;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
}

.tag .textLabel {
  margin-left: 8px;
}

.tag .colorLabel {
  width: 7px;
  height: 7px;
  border-radius: 50%;
  margin-left: 6px;
  margin-right: 6px;
}
