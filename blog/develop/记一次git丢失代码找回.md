---
slug: lost-code-find-by-git
title: 记一次git丢失代码找回
date: 2021-08-15
authors: kuizuo
tags: [git, code]
keywords: [git, code]
description: 记录 git 操作失误导致代码丢失与找回的过程
---

<!-- truncate -->

## 场景复现

今晚，我和往常一样对着电脑撸着代码，这时候我灵光一现，想到了一个好的功能，于是乎我就开始增加代码文件，更改之前已有的问题，当我实现完这个功能的时候，觉得可有可无，我想通过 Git 直接回退到我没有这个新功能的版本，把新增的文件和更改的文件全都给还原回去，然而在编写新功能的时候我忘记 Commit 了！！！（正常操作应该是新建一个分支，在新分支编写新功能），于是乎我点了如图操作（这里仅作为事件发生展示，并不为实际丢失个数）

![image-20210815141808996](https://img.kuizuo.me/20210815141808996.png)

没错，清空所有更改过的代码。导致这些文件直接丢失（并不在回收站），包括写新功能前的代码和写新功能后的代码全都丢失了 😭！！！

## 找回前提

庆幸的时候，写新功能前的代码我成功 add 到了暂存区，只是未 Commit 而已，那么就能找回对应的文件（仅仅只是文件，并且没有文件名，项目结构都无法还原 ）。如果有 Commit 的话非常好找回，直接回退上一个版本即可，如果连 add 操作都没有的话，除非像 VScode 插件 Local History 或一些 IDE 有记录本地文件，不然恐怕是真的找不回了。。。

## 开始找回

故，此次目的是找回 add 过而未 commit 的文件，首先打开 git bash 输入

```bash
git fsck --lost-found
```

![image-20210815150520759](https://img.kuizuo.me/20210815150520759.png)

进入`.git\lost-found\other`

![image-20210815153556495](https://img.kuizuo.me/20210815153556495.png)

然后通过文本编辑器打开即可，如果是代码的话重命名对应的后缀，如果是图片这些就得对应删除前所对应的文件链接。名字是找不回来了，只能手动重命名。

## 事后回想

可能这次丢失的仅仅只是几十个文件，下次丢失的可能就是一个项目了。所以在每次更改代码前做好备份才是首要做的，同时也感谢 git 这么好用的版本控制系统，不然这篇博客可能也不存在。
