---
slug: server-and-domain-beian
title: 服务器与域名备案
date: 2020-11-17
authors: kuizuo
tags: [server, cloud]
keywords: [develop, cloud]
---

<!-- truncate -->
## 云服务器

服务器说白就是全天 24 小时不停歇的运行一台电脑，同时分配一个公网 IP 给这个电脑，你只需要把你要的资源放置到这台电脑上，别人通过访问该 IP 就能访问到这台电脑的资源，比如你放一个网页部署在这台服务器上，别人访问 这个IP就能访问到网页的内容。

你可以根据需求来安装服务器的系统，这些在云服务厂商中都是可以选择的。

### 服务器的配置

一般来说，一些提供云服务器的厂商都会给新用户一个新用户价，差不多也就是 100 左右一年的云服务器,配置一般都是 2g 内存,1 核,1m 带宽,50g 硬盘说实话对于没有特殊需求的服务器够用了。而不是新用户的价格，这样的配置一个月差不多 70 元，算一下一年打折后 700 元，这还算便宜的了，服务器贵这很正常，全天不间断运行，电费，网费和一些服务费用，其实就已经非常值了。

其他几个配置没什么可说的，如果要说服务器哪个配置最贵的话，无疑就是带宽了。1M 的带宽理论上传速度为 128kB/s，也就是我从服务器中下载一个 10m 的软件，需要 80 秒，这还只是理论速度，我实测过平均速度不到 100kB/s。如果搭建网站的话 1m 有些慢，虽说一个页面一般都不会超过 100kb，但背后加载的图片 css js资源可就不只是kb大小了，用户访问网页就需要花费一定时间等待加载，体验非常不好。个人建议带宽5m起步，否则就不建议购买。

### 云服务器和轻量应用服务

关于服务器的选择很多人不知道云服务器和轻量应用服务的区别，这里两种服务器我都买过，且目前都在役。主要区别和优势请参考下表（腾讯云文档）

![](https://img.kuizuo.me/CO_V_ghsyo.png)

更具体的可以查看对应云服务商的介绍

[阿里云ECS云服务器和轻量应用服务有什么区别及选择方法-阿里云开发者社区 (aliyun.com)](https://developer.aliyun.com/article/1023850?spm=5176.21213303.J_6704733920.7.432353c9DbykNf\&scm=20140722.S_community@@文章@@1023850._.ID_community@@文章@@1023850-RL_阿里云ecs云服务器和轻量应用服务有什么区别及选择方法-LOC_main-OR_ser-V_2-P0_0 "阿里云ECS云服务器和轻量应用服务有什么区别及选择方法-阿里云开发者社区 (aliyun.com)")

[轻量应用服务器 与云服务器 CVM 对比-产品简介-文档中心-腾讯云 (tencent.com)](https://cloud.tencent.com/document/product/1207/49819 "轻量应用服务器 与云服务器 CVM 对比-产品简介-文档中心-腾讯云 (tencent.com)")

**总结：买轻量应用服务器是最实惠的**

### github学生认证送服务器

如果你不想花钱买一个服务器的话，可以考试github学生认证，会送你一个服务器。具体可到官方中查看 [https://education.github.com/experiences/virtual\_event\_kit](https://education.github.com/experiences/virtual_event_kit "https://education.github.com/experiences/virtual_event_kit")

![](https://img.kuizuo.me/Rsh8Y_TBfe.png)

### windows 和 Linus 服务器的区别

这里可能会说的不对，毕竟我接触服务器相关等配置也没太多时间，但是我觉得有必要说一下，我那时候用 windows 服务器的时候，只要通过 windows**专业版**（一定要专业版才能远程连接别人的电脑）自带的远程桌面（cmd 中输入`mstsc`即可），然后输入 ip 地址，接着在输入相应的账号密码即可，但有可能无法连接，原因是防火墙和 ip 白名单没有配置好，服务器不允许连接。

而对于 Linux，用的最多的就是宝塔面板了，但是连接不是通过 windows 远程桌面，而是通过像终端那样连接登录，我一般是用 Xshell 来连接。但是连接完就开始输入命令安装宝塔面板，然后会有对应的面板地址和登录宝塔面板的账号密码，只要访问给定的面板地址加上用户密码即可登录。像对应的界面如下

![image-20200918141519472](https://img.kuizuo.me/20200918141519472.png "image-20200918141519472")

## 选择哪家云服务器厂商

目前市场上主要有阿里云和腾讯云的服务器，这两者的服务器质量和操作体验上都属于大厂级别。不分上下，都可以选择购买，不过最好有个原则，你域名在哪购买，服务器就买哪一家的，因为到时候备案是需要服务器才能备案的。

# 域名

正常来说你访问你一个网站肯定不是访问一个ip地址，而是一个域名，比方说访问kuizuo.me，baidu.com。但其实访问域名就相当于访问这个ip，过程如下：首先访问域名会经过DNS解析，DNS（域名系统）会找到你要访问的域名所解析的ip，然后访问这个ip。

但有遗憾的地方就是很多时候想要注册自己想要的域名非常难，因为已经被别人现行注册了。比如我的现在的kuizuo.me这个域名是从他人手里购买而来的。所以域名这东西请优先准备好，备案解析啥的完全不急，一年也就是几十块钱，但是你不先买就很有可能给别人先注册，到时候想买都没得买，要不然就要花大价格购买，因此也有很多人去做域名买卖的生意。

一般一个域名够用了，需要的话在域名解析中去添加域名的子域名（二级，三级等等），比如我的一些个人项目就是使用二级域名来访问的，这里我也就不在列举了。

### 为啥要备案

首先备案要提交负责人的身份信息（身份证正反，手持，人脸，手机号，住址等），记录你这个域名内的网站的负责人和单位，主要为了防止在网上从事非法的网站经营活动，打击不良互联网信息的传播，能给予警告和封禁。（网站备案只针对国内服务器）

总之不要去搞违规违法行为，天网恢恢疏而不漏，网警要找总归有方法。

### ICP 备案

9 月 3 号购买的腾讯云与域名，然后进行初步的服务器简单部署配置，第二天开始域名实名认证，接着实名后需要 3 天时间才可以进行开始域名备案，等了 3 天开始域名备案提交网站的用途信息等等，然后拍身份证，人脸，手持，接着到 9 月 9 日腾讯云服务器的客服打电话给我要我修改一些信息，比如网站详情写的不行，资料不全，过不了备案等等，然后修改重新提交一次，最终等待收到腾讯云助手的通知，直到 9 月 18 号，如下结果

![image-20200918113449612](https://img.kuizuo.me/20200918113449612.png "image-20200918113449612")

至此 ICP 备案就搞定了，ICP 还算轻松，身份信息真实，来访电话及时接听，等就行了。

只要ICP 备案，就可以通过域名 [kuizuo.me](https://kuizuo.me "kuizuo.me") 访问到我的个人博客。(当然前提需要到域名管理中的DNS解析添加)

![](https://img.kuizuo.me/nuVA2RTh_b.png)

### 公安备案

但还有一个公安备案，虽说不是强制的，但一般都是建议去公安备案一下。我之前的域名kzcode.cn就有公安备案，但现在的kuizuo.me 并没有。主要原因还是太过于繁琐，比ICP备案复杂多了。

首先需要登录 [http://www.beian.gov.cn](http://www.beian.gov.cn "http://www.beian.gov.cn") 注册并且登录填写的信息也比上面的多，如下图

![image-20201119191204222](https://img.kuizuo.me/20201119191204222.png "image-20201119191204222")

填写资料折腾了半小时左右的时间，才提交上去，而且过了快两周后，我收到了如下的短信

> 【公安网站服务平台】尊敬的用户:您开办的网站(互联网技术文章分享:kzcode.cn)审核未通过，原因：网站信息检查有误，审核不通过，请尽快登录www\.beian.gov.cn网站，在草稿中修改并重新提交网站备案申请，如有疑问可在工作日（上午8:30-11:30、下午14:00-17:00）联系网警，联系电话：059xxxxxxxx，谢谢您的配合。

工作人员电话和我联系是说我户籍转了，需要在我转入地去申请，于是又重新提交改数据，反正就是照着腾讯云的帮助文档填写自己的个人信息，中途有几次就这么磨着磨着到了 11 月 17 号，短信收到了这一条

> 【公安网站服务平台】尊敬的用户:您的开办主体已经审核通过，如果存在自动关联的待备案或待认领网站，请尽快核对归属，进行新网站备案以及已备案网站认领的申请。如有疑问可在工作时间（周一至周五上午 9:00-12:00、下午 14:00-17:00）联系网警，联系电话：059xxxxxxxx，谢谢您的配合。

然而，我登录了网站却没有看到已备案的网站，于是联系工作人员 ，然后叫我重新提交一次，这次不到半小时就搞定，最终短信结果如下

> 【公安网站服务平台】尊敬的用户:您开办的网站(互联网技术文章分享:kzcode.cn)已经审核通过，请尽快登录www\.beian.gov.cn网站,下载备案号码，附在网站底部，如有疑问可在工作日（上午9:00-12:00、下午14:00-17:00）联系网警，联系电话：059xxxxxxxx，谢谢您的配合。

然后登录公安备案网站，查看备案结果如下图，帅的不谈！

![image-20201117152619474](https://img.kuizuo.me/20201117152619474.png "image-20201117152619474")

### 悬挂备案号

如果上面都已经弄好了也别太高兴，要在网页源代码将公安联网备案信息放置在网页底部。简单的说完成两项备案后都需要在网站页面底部显示备案号，并指明转到链接。在我的个人博客页面最下方，就会看到如下图这样。

![image-20201117153209037](https://img.kuizuo.me/20201117153209037.png "image-20201117153209037")

[工信部原文](http://www.gov.cn/gongbao/content/2005/content_93018.htm "工信部原文") 第十三条和第二十五条

> **第十三条** 非经营性互联网信息服务提供者应当在其网站开通时在主页底部的中央位置标明其备案编号，并在备案编号下方按要求链接信息产业部备案管理系统网址，供公众查询核对。

> **第二十五条** 违反本办法第十三条的规定，未在其备案编号下方链接信息产业部备案管理系统网址的，或未将备案电子验证标识放置在其网站指定目录下的，由住所所在地省通信管理局责令改正，并处5千元以上1万元以下罚款。

当然也有很多网站并没有这么做，具体还是需要看网站的性质，是否经营性网站。像个人博客这种，只需要悬挂一个ICP备案号即可，但对于绝大多数的国内网站是肯定悬挂公安备案，并且除了备案信息外，还有一堆相关证件，如营业执照，许可证，资格证等等，国内的网站监管非常严格的。