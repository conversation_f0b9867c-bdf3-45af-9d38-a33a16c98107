---
slug: 2021-year-end-summary
title: 2021 · 休学一年
date: 2021-12-31
authors: kuizuo
tags: [年终总结]
keywords: [年终总结]
toc_max_heading_level: 3
---

当写这篇年终时，都已过于数月了。今年 (2021 年)休学出去工作(创业)；加上 2022 年 1 月闭关安卓逆向学习，所以便没有抽出时间来完善年终总结。

所以说要写年终前一定要趁早，平常也要时刻保持记录的习惯，这样年终总结的时候思路才清醒，看到平时记录的点滴就能一时刻地回忆起所有细节。

每次写年终总结时不时也会潸然泪下，写的时候就需要不断的回忆过去，而往往过去的某些时刻的做法会让自己觉得是不是有个更优解？常常会回忆起过去这一年所经历的往往，难以忘怀，不知从何写起。

<!-- truncate -->

## Web 开发

在这过去的中时间内，我已经从一位逆向爱好者的转到 Web 开发行业上。在上次的记录中我也仅仅只是搭建了一个博客，还是基于 [Vuepress](https://vuepress.vuejs.org/zh/)，不过由于拖更过久，于是就索性使用 [Docusaurus](https://docusaurus.io/) 做为未来的博客。像一些主流前端框架 [Vue](https://v3.cn.vuejs.org/),[React](https://reactjs.org/) 以及 [Vite](https://cn.vitejs.dev/) 和 [webpack](https://webpack.js.org/) 构建工具使用过，期间也不断尝试新的技术栈，了解其新特性，所以实现些基本的前端页面或功能倒是不成问题。

但 Web 开发可不仅仅只是由前端页面构成的，虽然对于上面静态站点的博客而言，那确实够。不过想要做到一些页面阅读量，以及评论相关的，就必须涉及到数据交互，也就是后端服务。后端服务所选用的语言可就多了，例如 nodejs、java、php、python、go 等等，虽说都有接触过(尝试搭建过后端服务与部署)，但 node 还是我的后端开发首选， JavaScript/TypeScript 是我目前用的最多的编程语言，这其中就使用到[Nest.js](https://nestjs.com/)这类 Node.js 版的 Spring 框架，同时也接触到 [TypeORM](https://typeorm.io/) 这个 ORM 框架，操作数据就如操作对象一样，可以不用写 sql 就能完成基本的 CRUD。同时也接触到 Java 的 [Spring](https://spring.io/)、Php 的 [ThinkPHP](https://www.thinkphp.cn/)、Go 的 [Gin](https://gin-gonic.com/zh-cn/)、Python 的 [FastAPI](https://fastapi.tiangolo.com/zh/)，这些语言中的后端框架。同时数据库方面也学习与使用到 [Mysql](https://www.mysql.com/)、[MongoDB](https://www.mongodb.com/)、[Redis](https://redis.io/)、[Elasticsearch](https://www.elastic.co/cn/elasticsearch/)，这些 SQL 与 NoSQL 数据库以及搜索和数据分析引擎。

可以肯定是的未来的编程日子里有一大段时间估计也与 Web 开发息息相关。

不过在年终这并不想介绍在学习期间所涉及的项目，因为这太啰嗦了，导致有挺大一部分时间都是在介绍，而不是在总结，违背年终总结的意义。**(学习的)过程往往不是人们所在意的，人们往往在意的是所导致的结果。**

## 看书

也是在今天开始去看书/看文章，加固对已有技术的理解，基本上每天凌晨 0-2 点的时间段都在看技术相关的书籍。主要都是针对 JS 相关的书籍与一些其他书籍，以下是我今年看过的书

- 《JavaScript 高级程序设计》（第 4 版，简称红宝书）
- 《重构 改善既有代码的设计》（第 2 版，JS）
- 《JavaScript 设计模式与开发实践》
- 《深入浅出 Node.js》
- 《Visual Studio Code 权威指南》
- 《深入浅出 Vue.js》
- 《Vue.js 设计与实现》

## 经历总结

相比学习记录而言，我反倒是想总结个人初入社会中的一些机遇与所做（缺点与不足），以及未来遇到这类情景能否有改进的地方。

### 深思熟虑

反思我当时面对这种情况，我当时的做法是否合理？是否为最优解？是否有考虑他人感受？是否有考虑这么做未来有什么不利？是否...?

事情发生后能否做到不后悔？能否想过如何挽回？能否总结下次遇到这种情况又该如何做？...(语塞片刻) 还能有下一次吗？

很显然，在这一年当中我并没有很好的反思与总结，而是直到出现一些严重性的结果，我才会开始考虑此后果与弥补。

而休学便是我当时面对情景中的做法之一。

当时的我没有思考我休学后所会给我带来的不利，如后续学校课程的变化能否顺便完成毕业，是否想过休学后的生活，复学手续的办理，等等太多要思考的了，然而当时的我只思考到学校课程的无聊，不如出去工作闯闯，这可不比学校每天枯燥的生活来的丰富。我甚至还幻想着我休学出去，技术特别厉害，是不是回去就能直接免修课程，直接当大三来读。然而这种想法的天真程度不亚于一个三岁小孩问父母我为什么不能像鸟儿一样在天空中遨游，而现实生活是你只能在地面上爬行。

在学校跟着校方的课程，修满学分，遵守学校规定，完成日常内务卫生，方可毕业。而休学就是休学，休学期间学校只保留你的学籍，你在休学期间外面所发生的一切都与学校无关，如果在休学期结束后还未办理休学手续，则视为退学处理。

到了复学的日子，当时的早上我回到学校办理复学手续，准备重归校园生活。但由于疫情的缘故，学校是不让正常进出，需要使用学校 app 上申报进出码，在当时的我无论怎么申请都无法通过，提示找不到辅导员，因为我休学期间的又重新分配了新的辅导员，也就导致我无法申报进出码，当时的保安无论如何都要学生凭进出码才可进出学校，哪怕我把学生证，以及我休学时的手续，保安与辅导员的沟通，都不允许我进去学校。即便我家属陪同的情况下，依旧等了越 10 来分钟才方可放我进入学校。

现在回想当时如果我的家属没有陪同，也许这一天这个保安都可能不放我进去，保安估计是因为不希望家属在门口等候太久，同时也因为我复学的情况，所以破例放我进入学校，办理复学手续。

扯了这么多，也该说说休学的原因了。

当时（2020 年 12 月），我写了一个软件，并将其发布到我的 QQ 空间上供他人免费使用。有人加了我微信，简单咨询下，机缘很巧，他们的工作地点离我学校仅有 5 公里，于是线下交谈了下，问我有没有兴趣开发一个软件，并提供了一些想法和规划，当然，功能和需求与我所编写的大致。加之那段时间我已经厌烦学校所教的课程，与他们不谋而合，于是伴着辍学的心态办理休学手续，在 2021 这一整年“大展身手”。

这也就是为什么我 2021 年上半年没继续学习前端，没写博客的原因。在这期间，我基本上都是在忙着对该软件的更新维护。至于说为何要休学，明明离校那么近，可以边远程边线下办公。这主要还是与我的生活态度有关，我不希望我在做任何事情的时候，突然有其他的事情来打断我现有的阶段任务，哪怕只是一点小事，都有可能导致我难以进入工作状态。我忌惮的是在同时兼顾学业与工作，到头来很有可能两者都干得一塌糊涂（不过最主要我当时的内心是非常不情愿在上课的）。于是在学业与工作上两者无法兼顾到于是就休学专心工作。

说这么多，最主要是当时的我确实不是很想在校园里呆在，与其听着学校老师教的，不如自己出去社会闯荡一番来的实在。但最终的事实告诉我，还是出来太早了，社会的经验是不断磨练学习，而不是凭自己短暂实践与猜想的，只有**切身体会才能悟出真谛**。

又有点扯远了，总之简单交代下休学的缘由。而在今后的未来也会有一大堆这样的例子，而所发生过的甚至能写一天。**能做的仅有是保持一颗善良的心，与权衡自己内心的真实**

#### 如何考虑诸多结果

有很多种结果，并不是由自己所能考虑到的，哪有该如何是好。

如果之前的话，我确实不知道该如何，但现在我多半会把我所遇到的情况告诉身边的人(父母、朋友)，让他们帮我出谋划策。

他们所能给的也是建议，最终的决定权还是归咎于自己。事情的最终发展走向，也是看自己的表现。

### 人情世故

在没休学前，我的情商可以说是低的离谱，一点人情世故都不会有的那种。遇到一些情况我都很直面的揭穿，没有留有一定的台阶给他人下，没有思考这样做的情况对长期利益下的影响。这里我有一个亲身例子。

当时在工作中有位用户 A 反馈了一个网站的充值系统的 bug，这个 bug 可以导致随意给任意用户充值任意金额（没听错，这个 bug 的严重程度就是如此。不过当然不是我写的，这部分核心是由当时的一个外包公司搞得）。用户 A 交代了这个 bug 是由其他用户 B 告诉他的，并没有指名 B 的真实身份，原本 B 是想告诉 A 一起薅这个网站的漏洞，但 A 觉得不妥，想和我们长期合作，并将这个 bug 反馈给了我们。如果换做你的话，你又该如何处理这种事情。

当时的我第一想法就是找到漏洞，并将该漏洞修复了，但是是否有想过，在 B 告诉 A 没多久后，网站就把这个漏洞修复了，这不就明摆告诉 B，A 把这个 bug 反馈给了网站所有者，并且出卖了他。到时候 A 和 B 的关系又会如何发展？A 是否又会认为这个网站很没有格局？最终不打算长期合作？

而这些显然不是我当时所能想到的，而是我的同事所告诉我的。接着再来说说最终这件事情是如何处理的。首先我们第一时间肯定不是修复这个 bug，而是尝试去查询一些异常数据，例如大额充值，余额异常，对比真实流水与网站流水，尽快的确保找到这个 B 用户。不过这个 B 用户做事非常小心，这些数据与真实的几乎难以排查。于是我们尝试给这个 bug 加个暗桩，只要有用户触发这个 bug 就会将数据上报，最终找到 B 用户，也就是在赌这个 B 用户还会利用这个漏洞来进行充值。

果不其然，这个 B 用户还是抱着侥幸心理触发了这个 bug，然后项目的负责人直接联系到 B，然后暗示 B 用户，说他最近账号的活跃度这么高，金额有点异常。人总是有做贼心虚的时候，和 B 用户说了一大堆道理啥的，也说明 B 的这种行为是直接利用网站漏洞来盈利，其行为是有可能构成犯罪的。可能是把 B 说怕了，也是让 B 自己说出自己利用过 Bug，也说自己把这个 Bug 告诉了几个同伙 A，C 等人。最终，修复了这个 Bug，并一一联系这些并将其获利的金额给补齐，也就没在追究。（但实际上是有一定的损失的，只要这个 bug 被发现，能做的也只有弥补损失）

现在一回想，如果不是 A 主动告诉我们，也许这 Bug 可能在长时间都无法发现。最终这个 A 也成为了我们网站的合伙人。如果当时直接修复这个 bug，失去的金额是有可能找的回来的，但 A 这个用户却再也成为不了合作伙伴。但要说 B 会知道是 A 说的吗，他也许不知道，因为自始至终都未提及过 A，但他们之间的关系会有影响吗？与直接修复 bug 的相比，我看微不足道。

身边有太多为人处世的方法、道理和经验。

- 麻烦他人签字盖个凭证，如果态度不是很友好，并且无感激之言，会让签字的人所感厌烦，下次的签字是否会顺利？
- 遇到他人能否做到打个招呼，即便不是那么熟的情况下？
- 如果麻烦他人帮忙处理点事情，顺带带点吃的、送点礼物，那么他是否就会帮忙处理一下？
- 学会聆听长辈的建议，长辈所经历的远非后辈可言，都有长辈一定的道理，如果你只会觉得厌烦这些道理，那么很有可能一些长辈的经验只有自己吃过亏了才懂。
- 人都爱听赞美之词，能否在别人取得一定成果的时，给予一定的夸奖，对于他的心情是否也会变好？
- ……

有些人说他不想拘泥于生活的小节，想活得无拘无束。然而绝大多数情况下是不可能的，就像上面所举的例子而言，很多小节就有很大程度决定事情最终的发展。

**人活着务必要懂的人情世故，合理的为人处世往往会名利兼收，而不是狼狈不堪。**

### 行事低调

人在外面，要尽可能保持低调，不要展现自己过多的储备与能力。

就当我而言，我复学回学校正常上课的期间，很多学校老师所教的与所问的我都悉知，但我并不会第一时间回答，甚至是不回答。因为我知道如果我一旦回答，就会显得我很专业，他人会认为我的知识面比较广，可能就会来问我问题，交流技术。这本质是好的，一是能给同学一个相对好的印象，二是对班级同学学习氛围都有一定影响。但如果我不回答，对我有害吗？并不会。但回答就有优吗？也不见得。因为随着越来越多人询问，就可能会导致本该属于我的时间，却因为这些问答所耽搁。

至于是否回答与导致的结果，就需要权衡自身当下的言语行为是否对你有利，就比如对于新室友而言，我反倒第一时间说明自己有一定的技术水平，也在外面工作过，并给他们展示一些个人项目。因为我知道他们身边有个大佬，一些技术问题就有个大腿可抱，对于人际关系与求人办事来说都是对我非常有利。

不要泄露自身的“底”，这里的“底”对于上面的例子就是技术而言，而实际生活中，“底”有可能财富，背景，智慧等等。要知道社会处处是勾心斗角，越是让他人知道你的情况，往往是对自己不利。即便此刻的他对你所述的并无兴趣，但当他需要的时候，第一时间想到的就很有可能就是你。

“财不外露”是古人总结的经验教训，低调就是最好的自我保护。这是社会上的生存法则。

**一时的得意洋洋会换来以后的肠子悔青，一时的众星捧月会招来以后的众人愤恨。**

### 言多必失

我是属于那种说几句话，就容易滔滔不绝的那种。平常聊天中，也许对方只是想问下我银行卡号是多少，而我可能已经把银行卡密码告诉他（夸张点）。直白点意思就是话很多，非常容易说出一些本不应该说的东西。像以前如果有个人偷偷告诉我一个秘密，叫我千万不要说出去，而有时候不经意间就成为了告密者。然而我本意并不想泄密，但就是因为交际中，过度不必要的话语就往往。

当然，如果说的是自己的一些事情的话，对于他人来说，可能就听听就忘了。但有很多时候说着说着就说到他人去了，这时候一些过多的言语，就可能导致一些不恰当的词语加在他人身上。

我也许做不到**沉默是金**的实践，但我一定明白存在的意义。不善言，那就不必言。我之前就很喜欢给别人安利些东西，具体点如技术框架这些，但大部分情况下没成功安利，哪怕我对他当下的情况进行一定的分析，并告诉他使用了对自己的提升等等。而结果往往是将我的话语当做耳边风一般，也许是我的言语没有那么说服力，又或者是他不愿尝试新事物。所以这也就是为什么我现在不喜欢安利，不想多说些什么。即便说得再多，对我来说几乎没有任何利益可言，反倒是不说，没有任何亏损。

**能沉默寡言就不必口若悬河**

### 得意忘形

当一个人突然对你特别好，那么这时候就需要谨慎了，很有可能自己**沉浸在好处之中**，而**忘记所在的风险**。

就比如陌生人给小孩子糖果的例子，小孩子往往容易沉浸在得到甜味之中，而不会去想陌生人会将自己处于何种地步，当然这也和小孩子没有明确的自我辨别善恶的能力有关。但换到成人年身上不妨是同一道理，只是将这里的糖果换成了其他的好处而已。

这里我想强调的是，人一旦处于高兴的状态下就容易忘记一些行为。就如酒后吐真言，在喝酒兴奋的状态，是难以有任何的危险警觉，在喝醉的情况下，可能会把自己的一切说出去，而醒后根本难以想到当时兴奋的时候竟说过这般话语。

而多数人往往容易处于这种状态，对我来说可能就是别人夸我几句，我感觉非常得意，然后又和对方说起无关该话题的内容。然后这就回到上一个口若悬河话题。

也许可能会说，为啥要如此盯防这些潜在危险，因为坏人多数都是利用这种得意忘形的状态去坑害受害者。而能做的只有时刻预防危险，因为你永远不清楚危险的来临，当危险真正来临之时，就以措手不及。

### 急于求成

就如这次休学，我就巴不得早点工作，早点实现财富自由。但**过早的发育，往往会迷失方向**。

我日常编写代码的时候也是如此，有时候就是为了快点实现功能需求，就会去寻找相关功能库，就容易忽视底层实现逻辑。久而久之就成为了 CV（复制粘贴）工程师，导致一些学习本该了解的知识点，就因此忽视，直到别人的库无法实现自己的功能的，到自己实现起来可谓是愁眉苦脸的。

所以我现在心态也相对以前平坦了许多，没有之前的那种激情劲，或者说更加稳重，走一步都要稳一步。

有时候有些东西就应该顺从时间的发展，强行去改变它的发展方向有可能就得不偿失。还有，为何要急于去创造成功，而不是成功去找寻你呢？

**顺其自然，不失一个生活态度。**

### 虚假欺诈

[楚门的世界](https://movie.douban.com/subject/1292064/)中有一句台词对我印象特别深刻。

**外面的世界跟我给你的世界一样的虚假，有一样的谎言一样的欺诈**。

外面的世界即现实，亦或者是社会。确实，这里存在太多虚伪的内容，多数人对他人的表现与自身内心存在极大差异。举个例子，一个非常普通的人，面对领导时展现积极主动，面对朋友时展现情同手足，面对亲人时展现情同骨肉，对不同的人，都有不同的表现形式，而真实的他却只有自己最清楚。可以说人本就是很虚伪的，只是这种形式在交际中被放大。

除了虚伪外，很多的还是谎言。这里举一个不那么黑暗的谎言，多数人都喜欢装逼，吹嘘自己如何如何的。将自己包装得有多么厉害，地位多么显赫。其目的也只有自己最清楚，在被这种表面所影响下，就认为他事情都相对可靠，不然的。

所以言语上，并不能以百分之百的确信。再好比目前互联网的新闻内容或者是短视频，我都不会去对其真实性保持绝对，至少肯定不会是完整的。因为太清楚这些内容多数是以博人眼球为目的，也许会歪曲一定的事实，营造一个绝对火爆的效果。没置身于此地，又怎敢下一断言呢。

**俗话说得好眼见为实，不要轻信传闻，看到的才是事实。**

不要轻易的他言，越是真诚的人，越是容易被欺骗。

## 自我保护

这些就是我这一年的大部分社会感悟，然而事实上这仅仅只是皮毛中的皮毛，有很多不是那么友好的面我并没有用言语去展现，也难以展示。

但最终想说的是出门在外，保护好自己才是最重要的。无论是身体还是精力，起码活着是为自己而活。

同时时刻**当心**生活中的风险，无论大火小火最终都有可能酿成不可挽回的伤害。能做的也只有提防，警惕。

与社会相比，校园生活也确实安全多了。与你相龄的同学，很多都没有经过社会，做事处事都不会搞得如此复杂，但或多或少肯定还是存在的，只是相比社会而言没那么复杂。倒是可以说大学校园就是步入社会的一个缓冲区，缓冲区有多大，就决定在社会中你的发展效率。

## 回到校园

办理完复学手续，接触了新室友，新班级，依旧还是熟悉的校园，但此时此刻却感受不到校园的气息。内心也许还停留在休学一年的风光，可身体可却要老老实实在待上两年。

每日起床，打开屏幕，查看邮件信息，吃饭，能旷的课就旷，不能旷的课就尽量去，坐下敲代码，一天的日子就这样过去了。这半年里至少有百分之 80 的日子是处于这种状态。

要说枯燥，也有一些乐趣，要说充实，也会摸点鱼。不过，这不就是大部分人的生活方式吗？以混日子的方式做着重复的事，在重复中寻求一丝不同。

在学校也没啥特殊的要求，别挂科，在读两年拿到毕业证与学位证即可。还有两年的时间发展，也许这两年是程序生涯中仅存可自由分配的时间。

## 总结

要说我这一年的总结，**一切行为都一定要留后路**。

人生要做太多的后退路，才能够走出自己的路。过去的自己的很多决定都是不留活路，可以说是大部分的情况连后悔的机会都没有。当时的我为何要做得如此绝呢？仅仅只是鲁莽吗。不，所突显的是一个真诚的态度。如果做得任何事情都留有后路，只能说你是一个警惕的人，但不是一个真诚的人。但这个社会往往就是不需要真诚老实的人，在结果面前，这些都一文不值。越是真诚，越容易被欺骗。到头来，伤的还是真诚的人。

我高中因为一些特殊原因换过班，换完之后有一段时间非常后悔，失去(永别)日常课上课下与要好的同学。可结果已经发生了，很多东西就难以还原成最初的模样。上了大学第一学期结束，我就从数学专业转到软件工程专业，只为毕业时有个科班的资格，而现在科班在我看来一文不值，还要面临补修原专业没有的课程。但相关手续都已经办理完毕，即便撕毁手续，也无济于事。每一次的选择，都总让我觉得不值，失去的远比得到的多。

不过话也不能说的这么绝对，我高中换班让我远离那些特意”针对”我的老师（我高中学习表现特别差，属于那种差生不爱学习的，虽然现在也差不多）。我转专业也认识到一些志同道合都热爱编程的伙伴，我要是不转专业也许在这个学校里可能都难以有交集。同样的休学也让我收获了不少，社会经验与做人处事有了个质的提升。

所以高中的我特别渴求大学，在大学里不断的提升自我，也就是为什么高考一结束，我就沉浸在电脑面前，不是游戏电影，而是可创造一切的代码上。在上一学期后，想去软件工程软件。并且当我有了一定的代码基础水平，我觉得有能力去应付实际任务需求，也就办理休学手续，去完成一番“事业”。现在回到校园，同样的，我也非常渴求毕业，只为能够更早做出一番事情来。

如果有个选择让你在 40 岁前平平无奇，但在 40 岁后一鸣惊人成就一番事业，我想我肯定不会选择。我已经经历了比同龄人多的多的经历，我完全认为 30 岁就能达到 40 岁时的目标。有一个梦想叫别人 30 岁有的东西我 20 岁就要，这也是我的梦想。

为什么会休学，想必已经有了一个很明确的答案。而且我也很清楚，谁都劝不动当年的我。即便休学让我晚毕业的一年，同时还带来了各种琐事。在明知道结果的前提我依然还是会决定。

做技术写程序的不应该只有技术上的进步，更应该在友好的交互方面，这里的交互不单单是人与机的交互，而是生活中人与人，人与事之间的交互。

一个应用程序的实用性如何，很大一部分取决于交互。生活中圈子同样与他人的交互有很大牵连。代码写的再好，功能再强大，但是没有一个好的交互，用户同样不会使用。生活中，即使能力再强大，但对他人不友好、对工作不上进，也难被认可。

愿来年提升自我的同时，能够辨别是非，待人处世。修正自身缺点，发挥自有特长。不要高傲自大，不要妄自菲薄。不要只沉浸做事，而不去做人。保持原有的心态，不被大起大落所失衡。不断经历，不断收获。

愿一切安好，愿前程似锦。
