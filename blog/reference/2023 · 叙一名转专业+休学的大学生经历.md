---
slug: narrate-a-college-student
title: 叙一名转专业+休学的大学生经历
date: 2023-07-11
authors: kuizuo
tags: [年终总结, 人生感悟]
keywords: [年终总结, 人生感悟]
image: https://img.kuizuo.me/202312270109542.png
draft: false
---

<!-- 人生在世, 难免会有些挫折. -->

我一般很少做年中总结，但是这上半年发生在我事情比较多，加上毕业季，万千感慨涌上心头。

过得很快，本该在这个时间段毕业的我，因一意孤行申请休学一年，导致我比原同一届的人晚毕业一年。也正是这个决定改变了我的人生轨迹，让我成长了许多。

如今的我作为一名准大四的大学生 👨‍🎓，且经历过转专业和休学的大学生，来叙述自己的经历。

<!-- truncate -->

## 前言

如果你有注意到我的 [github contributions](https://github.com/kuizuo?tab=overview&from=2023-07-01&to=2023-07-19)，你会发现我有整整一段时间（约 2 个月）没怎么编写代码和记录博客了。在这期间整个人的状态很差，十分消沉，迟迟到现在为止。

在这期间我被**公安传唤**了。说难听点，我当时的身份是犯罪嫌疑人。当然现在应该也算，处于取保候审状态，除了不能出国外，目前还算自由。

:::info 补充: 暑假期间还想去香港旅游，去办理港澳通行证的时候被拒绝了 🤡。还想着去尝试办理 visa 卡（可境外金额交易），现在看样子结果已经毋庸置疑了 😔。

:::

至于案情我不方便细说，总之与互联网（技术）相关且在我休学期间出的事情。一开始关于这个话题我并不想提及，毕竟说出来肯定对自身有所不好，因此本文便迟迟都处于草稿状态。但结合自身经历，我认为非常有必要把这些内容记录下来，避免重蹈覆辙。

## 转专业+休学

我想大学内有这两项经历的人应该是在少数，我会把缘由交代明细。

### 转专业

我大一时的专业是 《信息与科学计算》，所属数学系，这个并不是计算机专业（虽然涉及到一些计算机相关的课程，但本质还是理学，不是工学），所以我转专业的原因很简单，就是要**科班出身**。当时就我看来科班出身能为我以后工作带来很大的帮助（当时的想法很天真，没有考虑转专业后所要付出的代价），但在这些年的经历以及行内的各个大神的能力告诉我，实际上并不会带来多大的优势。

我转专业并不难，因为我暑假就已经接触编程，并且在大一的时候，每天不是在写代码，就是在看代码，那段时间可谓是我人生编程学习最快乐的时光。所以转专业的考试对我来说特别轻松加上我数学天赋还不错，原专业的成绩也 OK，屁颠屁颠地将转专业填报单提交了上去，下学期便分配到新的班级中修学新专业——软件工程。

然而就在我转完专业，我便开始后悔转专业了。我发现学校老师所教的是什么牛马？真的就是会念 PPT 就便会教课，而且所教的多数内容，所发放的题材都是相对过时甚至被淘汰的东西。难怪说学生会对编程失去兴趣，要是我一开始跟着老师这么学，现在那有愧怍二字。

课程教学质量差也就算了，课程设计的要求还与学生课堂的内容还不同，我很难想象这学期只教 Web 前端（ES5 时代），却要求课程设计实现一个带后端接口服务以及数据库服务的程序。要不是我当时的基础还算好，恐怕连项目买来都不知道怎么跑起来。

不过这也是国内绝大多数高校现状，课程内容老旧，教案设计不合理，在越垃圾的大学中，这种情况反而更明显，恰好我就读于垃圾中的垃圾。与其抱怨教学质量，不如自己潜心学习，也正是因如此，自我学习能力才能有所大提升。

也不能说转专业对我没有一点帮助，毕竟自身有了一定的编程基础知识，在专业课上回答问题上我还是能说上一点的。并且每次课程设计与考试的时候总有人会找我来报个大腿，老师也见识到我的专业能力，我这一小组成员都能轻松通过课设答辩。（主要还是归功于我吹牛逼的能力）

关于转专业，还有一点就是补修。比如我大一是数学系的，当时的课是叫数学分析，而在软件工程专业就是传统的高等数学，运气还算好，这两门类别相同，可以做学分替换。但往往没那么幸运，就需要额外花时间去修之前的软件工程大一的课程，在跨度较大的转专业中甚至还会更多。

:::warning 警示

最后我想告诫一些有想转专业的同学，如果你能接受转专业的麻烦，并且真的认为转专业对你带来帮助，那可以转。但如果只是为了换个班级换个室友什么的，转后的代价或许比你与同学间不友好相处四年还要负重。**总之，转与不转，最终目的是以最快捷轻松的方式拿毕业证为主。**

:::

### 休学

然后在我大二上的时候发生了一个契机，我当时编写过一个易语言软件能够自动完成大学生网课视频、作业的[程序](/blog/chaoxing-helper)，并将其发布在网络上免费使用。

挺巧的是不久后，厦门当地（距离我学校也就 5 公里）的一个工作室（算我有 4 个人）恰好看到了这个软件，问我能否在此基础上实现的一些功能，也说明了他们的目的，想要一同合作，我主要负责技术，他们负责销售推广。我思考了下可行性，于是结合转专业的懊悔之下，我义无反顾地办理休学手续，开始了我的休学之旅，准备大干一场！

[](https://img.kuizuo.me/202312250425022.jpeg)

现在回想当时我的做法太过于任性，当时是 12 月份的时候，也就是临近考试周时，我便不在宿舍复习，甚至考试我都是直接没去的，而是直接到工作室里开始改写我的程序。以下是我当时学期的成绩单（有点惨不忍睹）

![Untitled](https://img.kuizuo.me/202307110122716.png)

我当时的想法特别天真，认为只要技术在身，天下我有。~~甚至当时都考虑花点时间学习渗透技术来入侵教务系统来改分（好在目前为止我都没真正接触过渗透技术，不然就真的是太刑啦）~~

还有就是我本想的是休学带辍学，也就是休学一年期间 搞的好就辍学，搞不好就复学，当然前者是比较多的，不然也不至于连考试都没去考。（现在回想太亏了，因为这些课程缺课就相当于挂科，我还要办理重修，但没办法，当时的我可不想着以后的事情）

至于说为何有辍学的想法，甚至对本科证都抱着一脸嫌弃。一部分来源于技术成就的膨胀，还有一部分来自校园生活。反观学校和身边的同学，基本处于无所事事的状态（混日子），不是打游戏就是刷短视频，虽说很听规矩，要上课去上课，要开会就开会，无一缺席，日复一日，年复一年，可没点规划，就想着如何不劳而获。说真的，就这种 B 状态，谁毕业不失业？谁毕业不打工？

**什么样的环境造就什么样的人**。一个地方的土壤会决定植物生长的是枝繁叶茂还是弱不禁风。至少现在我知道为何那些大厂都会优先面向 985 和 211 的学生。

因此我要办理休学的决心非常强烈，我父母与我姐不断给我劝告，告诉我要赚钱，要工作什么时候都来的级的以后都来得及，然而这并不是赚钱和工作的原因，而是受身边人的因素。如果要我与其同那些大学的同学这样混四年，那我不如出来实打实的干一年。（这话说的会难听，但当时我就是这么想的）父母犟不过我，我也和父母表明我肯定会拿毕业证。既然孩子有想法，那就让孩子试试。

---

在确定好正式合作之后，我便到工作室布置好自己工位，以下是我曾经的一张正在跑项目的图

![101688581490_.pic.jpg](https://img.kuizuo.me/202307110122808.jpg)

现在回想，当时动不动就加班到半夜，但是我对此没有怨言，甚至很享受这段时间。做自己愿意做的事情，我认为这就足够有意义了。不过这段时间持续不长，主要开发阶段也就是上半年的事情，而下半年由于本地疫情原因加上我身处老家，迟迟未能回到工位。而平常更多的任务是维护，兴致也没有一开始从 0 到 1 的热情。要知道维护是件非常枯燥且重复的事情，运营人员一有问题，就反馈到我这边，而当时写的项目又是协议项目，基本上每隔一阵子，我就要重新抓包，重新分析参数，重新部署。加上当时易语言的项目又没有 CI/CD，于是我维护完之后，就又要将易语言的项目丢到这些服务器上，我当时非常想写一个脚本，奈何我们的做单服务器基本上隔一段时间换一批，而这些服务器又是物理机（一台真实的远程机子），而非腾讯云、阿里云这种服务器，更没有批量备份，系统镜像这一功能。因此在安装环境和部署上可以说花费了我很多没必要的时间。（也可以说因此契机我彻彻底底的放弃了易语言，对于一个大型应用而言，它有数不清的缺点）

不过最终我还是复学了，因为在这个项目上，我只看到不断重复的日子，且项目并不是一个长久项目，在未来的某个时刻必定会被制裁（只是我没想到会这么快），最终随着一年的休学期限到来，在做了项目移交后，我便退出了该团队。在此期间自然挣了点小钱（不多，也就相当于阿里 P6 级别薪酬），足够我快活潇洒好些年了。

可好景不长，往往一个人在巅峰后的一段时间，必然要经历一次落差。

### 复学

办理休学手续非常容易，只需要家长知情并和学校说一下，学生提交材料，学校会给你保留学籍等待复学（期限为休学期间）。但办理复学手续可就不那么容易了，首先当时处于疫情下，学校是没那么容易进出了，需要通过学校的某个软件来申请，可由于我休学了，学院那边并没有将我的信息录入进去，导致软件压根无法证明我的身份。门口的保安可不管你进出校门做的事情，没证明就别指望进出学校。无奈，我只好联系我当时的辅导员，可保安也不认老师(真实情况就是这样)，就这样耗了 10 来分钟，保安看不下我母亲与我姐在旁边等候，才肯让我进去处理完手续。说真的，假设当天没有我家属陪同，复学恐怕就变成了辍学了。

进出大门的事情解决了，复学找老师签字可又没那么顺利了，具体细节我就不细说了，总之就是各种找老师签字，而有的老师今天在，有的不在，来来回回折腾了 3、4 天才把整个手续处理完，最终学是复了，书是也可继续读了，此刻的心还处在小孩子不愿上学的时代。

但仅仅只是一年，却正好让我赶上了学费和教案的变化。

休学是保留你当前的学籍，在你复学的时候，给你降级处理，将你插入到新的班级里，我原本是 2019 级的，我复学后变成了 2020 级。比较不幸的是，2020 级比 2019 级的学费还贵上 1000，我认了，毕竟这学校还有啥事情做不出来，涨点学费算什么（又不得不吐槽现在 2023 级的新生学费竟然还更贵！也确实，现在多数大学学费都上涨了）。这还不算啥，最主要是我们系的教授正好换人了，人换了也就算了，教案也跟着换，这就导致也就是我要比 19 级的学生还要多上几门课，并且有些课我还需要重新补修。妈的，什么坏事都让我遇上是吧。唉，谁叫我要休学呢。

而复学后学校发生的一些变数也是在我休学前未曾考虑到的，变相的让我毕业的难度增加。

:::info 补充

你可能会比较好奇，为何不边上学边工作，而是要休学。我当时有两种想法，一种前面也说了，就是抱着辍学的心态，这里也就不在赘述。另一种就和我个人做事比较有关了，我做任何一件事情的前提就是不希望有其他事物来打扰，尤其是学校的因素，我学校是比较极端，规定的特别死，你想要实习只能在大四这个阶段(并且大四上还得上半个学期的课)，需要将课程修完才可。因此就有了休学。

:::

**如果仅仅是转专业+休学的话，`其实`就没什么好看的了，不过需要这些的铺垫才有后面一言难尽的故事。**

## 事件之 👮🔍💧

在复学的第一个学期，在 2022 年 5 月 x 号的 22 点时，我工作室负责其他业务的同事突然打电话告诉我（语气还略带急促，我猜测是因为项目与他无关就放了），说我原先待的工作室的人都被抓了，说和我之前写的项目有关，叫我把相关代码全都删除了，这段时间不要联系他们。挂断电话后，我二话不说直接把我台式电脑搬到了其他宿舍，然后把我的另一台备用笔记本电脑放在桌上，我生怕 👮‍♂️ 第二天直接来我寝室来拿电脑。就这样提心吊胆几天，我内心暗自窃喜以为没事，毕竟我已经金盆洗手不做了，应该不会再追究吧，果不其然，越担心的事越会发生。就在几天后的周六中午突然来了一通电话，我一看 0xxxxx110 显示 xxx 公安局，完了看来是逃不掉了，但我还是抱着侥幸的心理，我选择了拒接就当我睡觉没看见（我当时确实因为这个电话而醒）。打了两通后没回应，又过了一段时间后，突然我姐给我打了电话，说我学校当地的派出所打电话给她，说联系不上我，然后叫我姐联系我看能不能联系上，顺便问我发生了什么事，我并没有告诉他，然而我心里很清楚，就是和我原本工作室的同伴有关。（至于说为什么 👮‍♂️ 有我姐的联系方式，我只能说我傻逼，每次学校填写什么表格的时候，有个家长联系人，我都是填写我姐的电话。也就是从这一阶段开始，所有要我填报的信息都是虚的）

然后我就回了公安局一个电话，和我确认完身份后，叫我带上 🆔  和 📱 叫我到学校门口，有 🚓  接~~送~~。我一上车后，就直接夺走了我的 📱（真），在车上就问我：”知道我们找你来是干什么的吗？“ 我默不作声，一直到了公安局后，接下来的安排懂的都懂，从中午做 📝 一直到晚上 8 点，从醒来到回校的期间别说吃东西了，连 💧 都 tm 没得喝。这一整个阶段就是要我交代当时写这个项目的负责了什么、盈利了多少、收款方式有什么，然后打开我手机的支付宝，微信，银行卡查我账户里的 💰，总之先把我号里的 💰 转到他们的卡里先，我当时和他们说微信里的 💰 我父母给我的生活费，也是我平常主要的消费方式，我也把相关的账单记录给他们看，确信后才没有将我微信的 💰 转过去。（这么一说似乎还挺良心）

然后 📝  做完之后，👮‍♂️ 说他们局长本来应该把我带到他们当地公安局一趟，可由于疫情的因素，就为我进行取保候审，（当然，这句话肯定是有吓唬成分），接着身份信息采集，打印账单记录，签字就不在话下了。至此在这个案件没结束之前，我都将作为一个嫌疑人处理。整个过程弄完之后叫我过几天再来一趟，主要就是二次笔录，在进一步确认哪些是“非法所得”。恰好我回去的时候还下起来大雨，还得用身体去遮挡着 👮‍♂️ 给我的那几份回执单，通知书。

![Untitled](https://img.kuizuo.me/202307110122809.jpeg)

:::note

你可能会好奇 👮‍♂️ 为何没有没收我的”作案工具“，首先我很明确的一点是我已经半年没碰这个项目了，其次我的“同伙”已经做好了相关笔录，也把大致的流程交代完毕，那么 👮‍♂️ 大概是已经掌握我做的部分了，再者我不像我的”同伙“第一时间被“逮捕”，距离第一时间已经过去了几天，我该处理的数据也都处理的差不多了，这时候再没收的意义也不大，得不到实质信息。

:::

这也是我第一次体验到被任人处置的感觉，很不是滋味，这与学生时代被老师批评罚站或者被家长训斥不同，讲不清楚的感觉。在这期间，到目前现在印象还非常深刻的一句话：**不如实交代的话，让你连书都读不了！**

不过现在回想都是吓唬话，一种侦查的手段，只为了不择目的的获取更多的信息。因为 👮‍♂️ 从头到尾都没有直接通知我校，而是直接联系我本人，假设人家真的想让我读不了，直接联系学校，说明这个学生的行为，那么我必定会被开除（用开除学籍来保留学校的声誉）。不过最终学校还是知道了这件事，放到后面再说。

回到宿舍我便将台式搬回到原本的宿舍，坐在椅子上沉思了许久，殊不知我已经一天没吃饭了，然而此时的我毫无饿意，复杂的心绪让我发了条朋友圈：

![Untitled](https://img.kuizuo.me/202307110122810.png)

至此，事实告诉我白白浪费了一整年的时间，并且此后还将给我带来了诸多麻烦。

但如今我回想这个事件，我都已经退出这个团伙近半年了，已经金盆洗手了，却依旧有所关联。👮‍♂️ 可不管你的解释，只要你参与了，获利了，势必要追究下来。如果我当时能够劝阻他们不做，或许如今就没有这么多麻烦事，可这仅存在于如果。

![Untitled](https://img.kuizuo.me/202307110122812.png)

回到这个项目而言，本质上确实不是很正规很传统，但也不至于黑产那种，更多的称呼是灰色产业。👮‍♂️ 给定性为 “**提供侵入、非法控制计算机信息系统程序、工具罪”**，但事实上这个程序并没有非法侵入服务器，我只是将用户的正常操作转变为电脑程序操作，将正常的数据包给模拟出来，可以省去人为操作的一个过程，而不是入侵对方的服务器，通过数据库的方式直接修改。在笔录期间，我还特意举例了游戏外挂和游戏脚本的区别，前者是实实在在的篡改数据，后者则是程序来模拟人为的情况，而我所做的部分就是游戏脚本的部分，根本不能算作非法控制。但 👮‍♂️ 不会按照我的理解，更别说检察院了，他们都只会认为这是在”破坏公平性“，那么就归属同一“恶劣”性质，就可以归属这个罪。总之我说的再多，说的再好，也都不及别人的一句反对。

### 时隔一年

这件事情过去快一年后，也就是取保时间要到之际（前段时间），👮‍♂️ 传唤几个当事人去他们当地的派出所一趟。对我而言，这段期间正好在读书阶段，此次之行大概率短时间内是回不来的，无奈之举，只能把自身的情况和辅导员说明清楚后，便踏上了不知用何形容词修饰的路。

在外地待了整整两周，住了整个两周的酒店。至于说过程也没啥特别的，**一切都以流程为主**，再次用 📝  核实了一下情况，从公安阶段移交到检察院阶段（此时一般就没 👮‍♂️ 的事情了），将材料都归递到检察院，等待公安这边解除取保状态(钱保)，再到检察院这边开具监视居住书，然后告诉我们可以回去等通知了。

所以情况就是这么个情况，抛开公安这边，回到学校的第一时间我就与辅导员汇报了情况，此时就不得不向学院解释我的情况了。

事实上学校是知道我这个事情的，因为当时当地的公安局的一位 👮‍♂️ 在与外地的 👮‍♂️ 配合，而这个当地的 👮‍♂️ 正好和我校学安处的老师比较熟，没过多久就找我谈了话喝 🍵（真），我将情况和他们汇报，但当时认为还没审判阶段，还只是嫌疑状态，所以就暂时不做处理。

直到了这次传唤之后，等我再次回到学校后，没过几天就被辅导员叫去和院长谈话了，在这谈话期间，我都一五一十的交代清楚，但听完我的叙述后，院长给我的态度其实并不友好。因为在他眼里可能也认为我编写的程序也不至于被公安传唤，再到检察院的阶段，便觉得我没有如实交代，说话的语气都带有警告之意，总之各种吓唬的口吻，院长也和 👮‍♂️ 说了几乎同样的话：“如果被我们查到事实和你所说的不符，那么我们这边会直接开除你”。最搞笑的是他还提了一下，如果被开除的话，也不要想不开什么的。就让我很无语，到底是安慰呢，还是警告。

![441703600436_.pic](https://img.kuizuo.me/202312262235233.png)

至于为何要这么说，我想有很大原因是因为学生的负面因素会影响学校的名誉（挣钱），我想读过大学的人应该很清楚。但在谈话的期间，我表明了我是在休学期间做的事情，只是在校读书期间被叫去了（在校期间都没接触这项目），并且 👮‍♂️ 是没有直接通过校方来找我（我想是 👮‍♂️ 也不想麻烦学校），可以这么说，如果当地的 👮‍♂️ 与我校老师没有交识，校方可能都不知道我这个情况。但无论我如何解释，校方还是会以你是这个学校的学生身份来对你进行处理。

![Untitled](https://img.kuizuo.me/202307110122813.png)

当然，我可以比较肯定的是，辅导员也不希望我出事，毕竟现在大学生如果出事，辅导员也是要背点锅的，但院长的话我就不清楚了，但从与他的谈话中，总感觉他不见得我好的样子。

不过要说学生身份是否对我有利的话，那答案是肯定有的。比如说开具学生在校证明，酌情处理等等。但不是说大学生就是免死金牌，这要是换成未成年人，那还说不准真是。

## 我对此事件的看法

我想大致的剧情就交代了差不多，至于这一年期间找律师询问，找关系，操作什么的，我就不细说了，篇幅来的太长，且谈话内容很复杂，现实很残酷。我不想将这些负面，非正能量的东西传递下去，我本还是相信会社会会变得越来越善良的。但惟有一点我明白的是，没有办不成的事，只有到不到位是事。自我领悟这段话的意思。

回到这件事情，在来说说我的看法（补充）。

### 利益

因为当时这个项目的收益都不是以公司的名义，而是通过多个下级使用私下转账到某个总账号（个人号），如果金额不是很大，那大概率也没什么事情，但金额一旦大，又没有缴税，这种做法无法就是非法经营。只不过项目涉及网络因素，且又有点灰产的性质，所以嘛。。。

从一开始这个收入形式就已经决定了 💰  的合法性，再加之利益摆在那，自然而然就被盯上。假设这个项目都没产生任何利益，没有破坏他人的利益，哪怕只是个灰产项目（开源），我认为 👮‍♂️  不会折腾到去找你的麻烦。可以说没有利益与负面影响，就不会这么多麻烦事祸降到头上。

### 不一定什么都要交代

在笔录阶段，👮‍♂️ 的手段都会比较极端，希望当事人能够积极配合，透露出更多的信息，还告诉你能够酌情处理。但很多情况下，交代的越多反而不是什么好处，尤其在诱导性提问会让你偏向本不是你本意的回答。举个例子（算是真实例子，我听别人说的）：

某人 A 入职一个普通公司，就当 A 做了半年员工时，突然有一天警察突击这个公司，叫所有人双手抱头蹲下，A 不知道发送了什么，到了做笔录的时候，👮‍♂️  告诉 A ，这家公司是一家诈骗公司，这时 A 开始思考，在一开始入职的时候确实不知道是诈骗公司，但 A 做了有半年的事情，慢慢也**意识**到自己做的就是诈骗相关的，但待遇不错，他也不当回事，也没有离职退出什么的。接着 👮‍♂️ 问他，你知不知道你做的是诈骗，这里假设 A 有几种说法：

1. A 是个老实人回答道直接回答道：我知道。
2. A 心里知道，且在与 👮‍♂️ 多次回答中交代自己的行为在他的认知下不是诈骗。
3. A 心里知道，可 A 嘴比较硬，死活不承认，甚至连诈骗字样都没说，一副装死的样子。且有一段这么回答：什么？这是诈骗？你要说是诈骗的话我肯定不干，谁会去干这东西。

假设你是 👮‍♂️ 你会抓谁，毫无疑问，1、2 是肯定要抓的，因为从回答和行为上都已经承认了是诈骗，哪怕只是一点都算。即便 A 想要翻供，可警察却说以第一次笔录上说准，说 A 当时已经都回答了，可 A 不知要如何反驳，甚至无法反驳。

但 3 的情况也许就不一定了，因为从 A 的笔录证词上，确实表明 A 主观不认为是诈骗，且是员工身份，雇佣关系，即便事实发生，只要诈骗的 💰A 没拿多少，而是工资形式，除非 👮‍♂️ 真的有 A 的诈骗实质性证据，那么 A 大概率是无事发生。

例子可能不是很好懂，但我想要表达的是：不要被 👮‍♂️ 的话语所带入，一切回答都要以不是、不知道为主，且不要模棱两可、含糊不清，这种回答在 👮‍♂️ 认为就是”肯定“，意味着是知法犯法。一定把自己装的什么都不懂，哪怕自己内心什么都知道，也要装成哑巴一般。

这就不禁让我想说出但不记得是从哪里看到的一句话: **坦白从宽，牢底坐穿；抗拒从严，回家过年。**

### 技术岗位或许是个高危职业

试想一下，在上面的案例中，这个诈骗公司的技术人员与客服人员对比，你认为那么最后的结局会比较惨。再比如某技术人员不小心写错了代码，并且将其部署到线上环境，导致公司损失重大，你说责任在谁？

这样例子有太多了，很多情况下出了事情，技术人员要占据非常大的责任。但伴随这份责任的风险，也伴随着巨大的收益，风浪越大，鱼越贵。

## 后悔？

如果说没有这些变故，那我毫无疑问是无悔的，因为挣到了本不属于我这年龄段所拥有的资本。

论事实，这些资本化为乌有，同时耗费了一年的时间的情况下呢？那必然是会后悔一部分，我后悔的是没有听从我父母的劝导，在合适的时间做合适的事情，给家里人带来了许多的麻烦；而不后悔的是这段经历，让我成长了太多，让我如此膨胀的内心收敛的甚许，也让我在做任何事情都要三思而后虑，而不再一意孤行，固执己见。

提前发现自身的错误，及时纠正，以免重蹈覆辙。有很多道理也是在我经历之后才深刻明白的，只不过这次学习的“学费”比较贵重。

**年轻难免犯错，但也正是从错误中成长。**

故事的经历就告一段落，未来的路还很长，要考虑的事还有很多，有机会在慢慢叙述。

## 一些题外话

我想听完上面的叙述应该能解释的通这近两个月的状态情况。

我本以为奖励自己搞台 MacBook 能够将状态调整回来，然而不到 1 周的时间变又开始萎靡不振，对一切新鲜事物失去了好奇感。已没有当初纯粹的兴趣，留有的是对生活的无奈。找寻不到花费一晚上解决一个 bug 的成就感，留有的只剩错中复杂的需求与枯燥乏味的工单。

我开始思考问题的所在，如何长期保持某种状态。最关键的因素莫过于情绪，曾经的我能够保持不断学习的状态正是因为内心无忧无虑。可时至今日，伴随我的是焦虑、压力、不安、迷茫，**总觉得心里悬着什么东西放不下**，可至今我也没能找到很好的解决方案，也许等案子结束，一切都将会从容自如。

我曾收到一本书，名为《谁的青春不迷茫》，到现在为止我也没看过，为什么呢？就书名而言，在我收到这本书时，我不认为我有何迷茫的地方。可现在我又想重新拾起这本书，却不知这本书被我放置何处。

### 对我来说的本科

在对这个社会没有认知的前提下（即休学前），我自认为能力是完全能够胜过一本的证书。外界都说没有本科不好找工作，现如今怕是本科都不好找工作咯。就我事实而说，我的几份工作都不是本科给我的，而是我的项目经验，所以也因此我更加坚信我自己对本科证的蔑视看法。

但有两段经历(谈话)，改变了我对此的看法。

1. 在我与一位 40 岁的技术人询问过一些问题，其中一个问题是问国内读研还有必要吗，人家没有直接否定，而是直接告诉我，很多公司现在阅读简历会更偏重于看第一学历(本科的学历)，也就是看你本科学校好不好再看你其他学历，至于为何想必不用多说，高考考进清华和研究生考进清华的难度便知。

   而另一个问题就是问他本科有没有必要，而就举了他曾经项目投资的例子，说如果投资人发现要投资的项目的负责人没有本科学历，人家可能就不会投资了，大致意思是这样。

2. 一位国外华人的 hr，看到我写的一个作品，并且他公司正好急招一名相关的开发者（远程开发），便于我交谈了起来，然而他没想到的是我竟还未毕业，而他们招聘的最低要求便是本科生。然后又进行了一番简单交谈下，得出的结论是：以我目前的身份只能开实习生或高中学历的薪资（薪资差距在 4 倍左右，国外的待遇我不说具体的薪资你大概也能猜出有多少），hr 给的建议是对我来说就是拿着点薪资就有点浪费时间，于是便错过了这个 offer。

我觉得没有什么能比收益更具有说服力，因此也确确实实改变了我的看法，至少在中国是这样的。

就单从社会展示的数据上也可以知道，自考成人本科的人是越来越多，很多公司招聘都是本科起步。没经历只看数据对我而言是意识不到其重要性的。

本科固然重要，可反观现在中国的大学还是用来学知识培养人才的吗？我更倾向于说是用金钱消耗时光最终换来一本证书，至于这笔交易值不值这个价，我想多数大学生的内心已经有了答案。

如果要说大学对我来说最大的作用的一件事，无非就是白嫖各种大学生认证优惠（如苹果的教育优惠，github 学生认证，学生票等等），确实给我省下了不少的钱。除此之外，我很难再说出第二个实质性的作用，对我而言是更多的负面作用，例如高学费、断电断网以及学校一些系列蛮不讲理的行为，这种作风会在私立大学之间不断扩大。

### 感悟

在之前我是比较浮躁的，总想着如何一步登天，并且我与很多平常人不同，我是不走”寻常路“，不遵循“规则”的人。绝大多数人愿安安稳稳，但我可不，我宁可去冒险一番，却又不曾思考过失败的后果。

我很讨厌被安排，**被安排的人生，活着有什么意义？** 没有自己的主见想法，不断被灌输他人的思想，这便失去了我对生活的意义。不能做自己想做的事情，那真的是一件极其可悲的事！

但现在让我重新做一个选择我肯定和我原先的同学一样是安安稳稳（混日子）拿个毕业证，求稳已经变为了我当下一个很重要的标识，只要主观上有风险的事，哪怕利润再高，我也不会尝试一番，我经不起风险，至少在我没有绝对”保险”的前提下。

可如今的现状又怎么走的了宽敞，平坦的道路。也许生来就不应该平淡度过，最终要身处何处才能对的自己颠沛流离的一生。

---

人总是不愿听闻他人的建议，尤其这个建议还是长辈给予的。可多数人总会认为自己就是最正确的，自己的做法就是最优解，过度的自信很难看清自己几斤几两。长辈的建议是用他的人生阅历换来的，而你的想法是凭空而造，不切实际。（说的便是我自己）

很多建议仅仅只是传达给他人，他人即便意识到有道理，但也通常不会有所改变。只有自己亲身经历过后，回想起他人的建议是多么的宝贵。所以我也渐渐不怎么再给别人安利，反而是一种浪费口舌的行为。

---

曾经我是一个特别念旧的人，时常会怀念过去的巅峰、美好。因为在落魄之时，也能想起曾经所拥有的，让我重拾信心。能让自己不断前行的事物只有自己所经历的过去，而记录便成了我仅有回忆方式。

可现实却是不断打击我曾有的美好，好像上天就巴不得我好的样子。可没有什么上帝，任何事物所发展的结果都将有始有终，自己做的事情没有人比自己清楚，我曾怪罪自己运气不好，现在我意识到是努力不足。没有不幸之人，只有懒惰之徒。在绝对的实力面前，运气再差的非酋都能化身变为欧皇。

曾经有个人告诉我，凡事都要往前看，**只迷恋于过去只会让自己活得越加困难**。可当时的我怎么可能懂得这个道理，活在过去的美好之中，忘却了当下的目标。

不知道自己当下要做什么，是因为不知道未来要干什么，没有目标的未来，犹如没有罗盘的航行，在茫茫大海之中无处漂泊。

借阿甘正传的一句名言：

> _You got to put the past behind before you can move on._(你得把过去抛在脑后才能向前看。)

## 四年的编程

```jsx
const timeline = ['易语言', '逆向', 'JavaScript/TypeScript', 'Web 全栈']

timeline.forEach(time => console.log(time))
```

回到技术角度，随着我的 4 年编程经历就这么过去了，和曾所预期的技术要求还差之甚远，没能给自己一个很好的技术交代，没能在这四年结束之际进行编码，没能给这段经历一个完美的句号。

倘若没有编程，我甚至都不知道自己应当从事何种职业，可以说编程是我的再生父母，没有 4 年前那场偶遇，便没有如今的我。

**道阻且长，行则将至；行而不辍，未来可期。**
