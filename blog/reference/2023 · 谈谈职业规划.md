---
slug: 2023-year-end-summary
title: 2023 · 谈谈职业规划
date: 2023-12-25
authors: kuizuo
tags: [年终总结, 工作]
keywords: [年终总结, 工作]
---

又到了年底写年终总结的时候了，说实话今年感觉没什么内容可写。上半年发生了比较多的事不方便叙述，而下半年我忙于学校课程 + 课程重修，过得其实还有点浑浑噩噩。

不过如今都大四了，也确实是要考虑实习的事了。我想结合我自身情况，谈谈我是怎么看待工作或者往远点说职业规划方面的想法。

<!-- truncate -->

## 一些经历

### 工作经历

我目前一共有 3 段工作经历

1. 休学一年在厦门本地某工作室(共4人)与他人创业。 <span style={{ float:'right' }}> 2021.1 ~ 2022.1</span>
2. 在北京的一家公司(规模不大)远程实习。 <span style={{ float:'right' }}>2022.8 ~ 2022.10</span>
3. 在 [3R 教室](https://3rcd.com) 兼任助教与开发组成员。 <span style={{ float:'right' }}> 2023.1 ~ 2023.12</span>

事实上，对我而言也仅有休学的那一段才能算是工作，而其他两者从年限与工作性质来看不能算是一份真正的工作。但也不能说不是，至少也替别人做过事、签过合同、领过工资的。

不过我想稍微提提我是怎么找到这三个工作的，或者说这三份工作是怎么找到我的 -> **全靠分享**

我不止说过一次分享的重要性，我的第一份工作就是在网络分享了[一个大学生自动完成视频、作业的程序](/blog/chaoxing-helper)，恰好被我当地一个工作室的同事看到，寻求我能否改进功能一同合作，于是一拍即可，便开始[休学](/blog/narrate-a-college-student#休学)。

剩余的两个工作同样也是，一个契机是我当时正好在研究 [Strapi](https://strapi.io) 编写了一篇文章，这家公司恰好用到这门技术；而 3R教室则是因为创始人所用的与我博客相同的网站生成器 [Docusaurus](https://docusaurus.io/zh-CN) 在这个机缘下认识的，后来创始人创业开了3R教室，我也就利用业余时间在其中扮演助教身份，赚个零花钱。（其实我很热心肠的，哪怕我不当助教，只要时间允许的情况下，你有问题问我也会尽数回答）

### 接单经历

同样的，分享也能很大程度上提升你接单的资本。

我并非想要炫耀什么，而是分享实实在在给我带来了很多好处，我希望你能够分享一些内容，一些见解心得，哪怕是一些笔记、对他人问题的回复，都可能给你带来一些意想不到的好处。

我就有一个同学将自己的笔记、课设以及每次专业课期末考试的文档放到 CSDN 上（没错就是那个 IT 界的毒瘤），现在这不到期末了嘛，他的私信就有一堆人找他写课设啥的。

回归到正题，我接过单不多也不少，绝大多数是那种对我而言挺简单的问题，但对于一些人而言就比较困难。例如前几天一个例子，我编写过 [js-deobfuscator](https://github.com/kuizuo/js-deobfuscator) 一个 js 混淆还原的工具，正好有人需要将一份混淆代码还原出来，于是寻求我的帮助，发了个红包给我。有时哪怕只是回答别人一个问题，甚至都可能会收到来自他人的红包。想想看，你在技术群里是不是有过这个现象。

还有一个网站的单子我印象很深，是一个用于销售流量、虚拟会员等商品的网站，不同与普通的商城系统，充值是通过卡密，使用卡密到特定的网站上使用。当时一个网站全套 5000，客户也算熟人了，还帮我推荐几个人购买，相当于这一套网站(模版)，帮我赚了几 w，还省去我很多开发成本，后续我都无偿给他提供技术服务。

事实上至少现在为止我并不是很喜欢接单，也很少主动接单。小的单（也可叫零活）通常以一顿夜宵作为回报或是看在人情的份上；而大的单（通常为外包单），通常要求交付时间快，很多时候只为了更快的完成功能，而不考虑代码质量。并且通常没有维护性可言，就更别说写测试和重构了。写久了，代码虽然写的是快了，但堆屎山的速度都堪比屎壳郎了。代码能力的提升反而不是很大。

在我看来当因某种目的收了他的钱财时，往往就要花费时间精力去完成这个目的，而从我内心上很难平衡这点。所以只要我不收钱，我就可以不做事了（bushi

### 赚钱经历

我赚的第一桶金其实还不是我工作，在上大学前我已经赚过对我两桶金了，还全都是依靠网络上的资源，而非现实打工。一次是初三当时向别人学习如何刷钻以及购买钻卡来接单帮别人刷钻，另一个是作为线报群群主（现如今更多的称之为羊毛群，但我更愿称撸界），发布一些活动和教程通过拉人头引流赚钱。而也是再次契机之下，学会了开发(定制)软件。

这并非本文重点，因此我并不想过多介绍，但或许这会作为后面的铺垫。

不过我还是想说：**赚钱不易**。我现在回看我过去的一些经历，只能说运气成分很大，而又恰好在风口之中。在如今的互联网环境下，想要复刻曾经的路, 无疑是死路一条。

## 为何不实习？

背景介绍完了，那就来说说实习的事情，为何我都大四了，还不找实习？

不是不找，而是很难找到满意的。我在今年暑期的时候有尝试找过，投过几家大厂，无一例外，了无音讯。这对我当时而言，打击还蛮大的。因为三流学校出身，确实很难过（双关），加之没人内推，海投基本上是没希望的。

如今前端的职位卷之又卷，僧多粥少，在厦门一些中小公司所提供的前端实习待遇（薪资大约在 3k~4k），况且对自身的提升并不是特别大，在我不是一笔性价比划得来的买卖。

反观学校的情况那可就更头疼了，你敢相信在大四上最该实习的时候，极为不合理的教案却还要给学生安排课程，还是专业课的那种，以下我的课表。

![](https://img.kuizuo.me/2023/1231064437.png)

此外，我还需要重修当初因休学而没去考试的几门课程（还挺多的，重修费还交了我不少😢），导致我最后一场考试时间是在 12月中旬。还是强调那一点，我不希望工作与学业这两者同时进行。关于这学校的诸多不满，待我毕业后我一定要说说，感受什么叫中国的私立大学。

此外还有一点，也是我不想提及的一点，这段期间我是处于“监视”状态，内心总悬着一个不知何时爆炸的炸弹，生怕突然爆炸将会打乱我的行程。

好在如今期限已到，心里悬着的一块石头终于放下来了，如释重负。

于是在上述的因素下，今年下半年我就不想找实习了。

## 为何又想实习了？

最主要的一点就是以我目前学生(应届生)的身份，是可以有机会争取到一份好的实习的，乃至是大厂的实习。

其次是我并没有一个很好的项目演进经历，看完我的一些工作经历不难发现我所待的公司/工作室的体量都不大，甚至我到现在都没真正体验过打卡上班（当然我也希望不要有）。而这就是小厂或者初创公司的同病，各个流程所要负责的任务很模糊。我当时休学和他人创业工作，基本上都是我一人负责项目开发；第二个远程实习也是我当时主动退出的，因为工作形式与接单无疑；第三个就不用多说了。

尤其是在技术层面的团队协作之中，还缺乏相当多的实践经验。所以在自我分析下，为了学习某些只在公司才能学到的东西，就非常有必要到大公司去一趟。

我大概率以后是不太可能再考虑坐班，所以这或许是我仅有为数不多的上班经历了。不过我想既便真正要开始打卡上班，要开始适应国内 996，未来我铁定会后悔有过这段历程。

![1703021156782.png](https://img.kuizuo.me/202312250547469.png)

### 那么该怎么找呢？

那么在如今就业形势如此严峻的时代，我又该如何找工作？

关于这点我其实并没有什么很好的经验分享，这也算是我首次主动找工作。不过我可以肯定的一点是，只单靠海投与某些招聘软件，想要拿到一份心意的 offer 很难。若是能通过一些关系，牵一条线（走内推），才是最佳选择，所以多积攒人脉交际圈是很有必要的。

这一部分我想待我后续找到工作后，再来做个心得分享也不为迟。（不说了，我去准备项目与简历去了）

:::success 补

[记 · 在 AI 公司入职一个月的体验与感悟](/blog/experience-of-an-ai-company)

:::

## 远程工作

[电鸭](https://eleduck.com/)的sologen：**只工作，不上班**。很好表明远程工作的意图。

远程工作（remote work）是我当下认为最具性价比的工作形式，你可以过着四五线城市的生活水平而不用考虑一线城市的房租与消费，却赚取一线城市的薪资。往大点说就是挣美元花人民币。

但是远程工作并非多数人想的那么轻松的，很多人对远程工作有个误区，就是可以自由决定上班时间，很自由。其实不是的，不用上下班通勤，那就把通勤的时间拿来工作。没有额外房补开销，那就拿来压低工资。而该开的会还是得开，说白了就是换个地方上班，懒床是睡不了一点的，好一点的是冬天起来不用遭受寒风的洗礼罢了。

有的远程工作还会要求你记录每个时间段(细到每小时)你都做了哪些工作，我暑期的就有一份我老师推荐的远程工作(实习)就是这样，我就没干了。要论自由，接单/自由职业是最自由的，项目进度、时间都由你自己把握。

此外还有一点就是技术栈的因素。我并没有跟随国内的主流的 Web 开发技术栈去学习什么 Java，也庆幸还好当初认为 Java 这门语言繁琐的要死，让我转变使用 JS/TS 来进行 Web 全栈的开发，而如今的重心也是在 JS/TS。

而这套技术栈在国外的远程工作的岗位占比很大，一些初创公司也会采用这套技术栈。而我本身学的就是 JS/TS，自身优势反而更能体现出来。但反观在国内绝大多数公司我也就是老老实实做前端的那种。况且如果只想靠国内的主流传统的技术栈而去争取一个远程岗位，我认为当下还是很难实现的。就国内的职场环境下，如今工作都难找了，就别说本身岗位就少的远程工作了。

## 正常坐班

退而其次，那就正常上下班通勤，而这又有的抉择了。就以我自身举例把，我老家福建宁德且我在厦门某三流大学读书，意味着我只要在福建本地找工作，就可以过的相对来说舒服，人脉资源、衣食住行都不用过多考虑。但倘若选择在外打拼，意味着可能要适应陌生的城市生活，建立起新的人际网络，面对未知的困难和挑战。而这其中所能利用的、所要遭受的，都得由自个儿来承担。

今年冬至的时候我与家里人商讨过这个问题，家里人的意见更多偏向于留在本省，只求我稳稳当当，脚踏实地。因为我从小到大独立性很差，加上我确实有那么亿点宅，到外面恐怕是吃不消。所以在他们眼里哪怕在本省薪资不是令我那么满意，但至少可以过一种相对舒适的生活。而我自己则认为有必要去外面见见世面，有时候很多苦只有自己吃过才知道有苦。

不过最终如何选择，还得取决于我自己。这对于多数人也是值得花时间思考的。

最终如果选择重归故里，那么一开始就身在其中是否会更好？又何必在外漂泊，独自承受着一切。

### 我对打工/上班的看法

我经常拿我高考结束的那个暑假来做例子，我的一些高中同学去当外卖骑手、餐厅服务员，将他们的假期时间拿来打工赚钱，而我却埋头苦干的[学习编程(易语言)](/blog/2020-year-end-summary)，以兴趣驱使我学习一门技术。

或许是因为有过几段[赚钱经历](#赚钱经历)，加上家境和个人意愿，所以至今为止我都不可能会去从事这种回报率不高，或者说没有“未来”的工作。与其打工/上班，尤其是耗费大量时间与体力劳动的，不如将时间拿来提升自己综合能力。（个人观点，或许有些极端）

可能是由于受自由的影响加上年轻有精力折腾，同时自己又是那么不走”寻常路“、不遵循“规则”的人。所以像那种安稳平淡，重复劳动的工作，如体制/编制内的职业就不太那么感冒。

但从现实是没人想打工/上班，却依旧有人从事这些工作。绝大多数打工人别无选择，因生活所迫，只得忍受和抱怨打工/上班的痛苦，却又寄人篱下。

## 期望的工作

要说不工作是不可能的（但打工是不可能的），或许是因为这几年的一些工作经历有关，导致我有点习惯了远程，加上网间传闻 996 的压力下，我现在对坐班甚至还有些厌恶。对我最理想的工作环境就是能够自我在决定在某个时间点是否工作，例如在工作日放假，在节假日上班。

接单虽说也是，那这毕竟不是一份正经的工作，更多意义上来说是份兼职。更多是作为一时之需，接单又不可能接一辈子（搞得工作能工作一辈子似得）。

而我想说的是做个自由职业者，在细一点也就是独立开发者。但是现在来看这并非易事，不仅需要一定的技术能力和营销水平，最关键的想法就已经让绝大多数人对这份职业扼杀在摇篮中。

## 现状

首先我对自己的生活方式定义是：**偏向于自发的能量爆发，而不是有条理的持续努力。**

今年我的 github contributions 出现过了几次长时间的空缺，那段期间我基本上没有在写代码，而是将心思用在其他事情上。例如只打游戏（好在游戏如今也是戒了）、只想摆烂（好在是摆的有点多了）、只想复习（好在该考的试都已经考了）。**这就导致很多时候我会将全部精力专注在某件事情上，而抛弃其他与之无关的事情**。

这也就导致了我很难养成一个良好的习惯，我的生物钟也因此而发生巨变。贴一张极为离谱的作息

![Untitled](https://img.kuizuo.me/202312250547470.png)

总之持之以恒在我这还不存在过，**多数的开始是因一腔热血，而最后结束时却草草了事**。

我想是是过度的自由造就了这般现状，想要解决这个问题，坐班可能还真可以。

---

或许是因为比同龄人提前踏入过社会，有过几段工作经验，所以在就业形势严峻的时代，其实我反倒不是那么慌张。

何况目前经济状态也还算正常，哪怕不找工作也足够养活我自己好一整子。至少相比同龄人下，我已经挺富裕了。也没必要搞个打赏在自己的网站或项目上放置收款二维码等无关项目信息。对我而言，编写这些的目的不是为了接某个单，赚个打赏费，更多是一时兴起，分享给有需要的人，希望让其更为纯粹一些，仅此而已。

我对赚钱的态度也很简单，悦己便可（做点让自己愉悦的事情），数额能够维持自身生存便足够了。不过现在我还没开始到还贷的境地，或许只有压迫感来临时，才会让我激起对金钱的渴望，改变对金钱的态度。

**取悦自己，生活最好的心态。**

## 感慨

接触 web 开发算下整整两年半的时间，在这期间我并没有很好地扩张自己的技术面。在折腾方面确实不如以往，学习主动性也欠缺许多。不同于一开始所学习那样，喜欢瞎捣鼓，看到某个东西就会想尝试安装。

明年也就 24 岁了，面对即将到来的本命年，下一个阶段的走向其实很迷茫，面对我的是留学还是工作，我到现在都没有定数…

虽说我现在大四还在读，但其实我已经比同龄人晚毕业 1-2 年了（此时怀着感慨的泪水不禁流下）。心智上还保留着校园少年的青雉 ，同时也多了份成熟的稳重。

有过这些经历让我收获了更为珍贵的经验和独特的成长路径，我想未来的日子必定是丰富多彩的。

## 往年回顾

- [2022 · 逆向到Web开发](/blog/2022-year-end-summary)
- [2021 · 休学一年](/blog/2021-year-end-summary)
- [2020 · 编程之旅-起点](/blog/2020-year-end-summary)
