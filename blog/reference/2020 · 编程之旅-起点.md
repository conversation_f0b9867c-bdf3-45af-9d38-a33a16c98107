---
slug: 2020-year-end-summary
title: 2020 · 编程之旅-起点
date: 2020-12-30
authors: kuizuo
tags: [年终总结]
keywords: [年终总结]
---

写篇年记，记录一下自己这一年的所学。

能有幸在这个行业有两个关键因素

1. QQ 永久冻结

2. 易语言

<!-- truncate -->

### QQ 永久冻结

有些认识我的人可能会知道我的 QQ804493238 给永久冻结了，可以说这个号码是不可能再搞回来的。一个幸幸苦苦养了十年的 QQ ，说没就没的那种，与之相对应的就是游戏账号没了，没了游戏能干嘛，当然不能干嘛，生活还是得过的，但又要有个东西来打发时间，没错就是编程。于是高中毕业后的暑假，就开始了学习编程。不过这里要先介绍一下易语言，作为我的第一门编程语言。

### 初识易语言

初识易语言的时候还是在初中，那时候有个同学给我讲诉了他用易语言刷 CF 永久枪，用易语言写游戏外挂的故事，也是那时候我也才刚接触网络游戏，一把永久枪 888 还免费刷，别说有多牛逼了好吧。可以说从那时候开始，下了个目标，以后有时间一定要学易语言！（不过那时候没条件学或者说是给游戏耽搁了）果不其然，在高中毕业后，就开始了易语言的学习（最主要的原因就是号没了，完全没有心思再玩游戏）。

有关易语言的详细介绍我划分在另一篇文章 [易语言](/blog/easy-language)

### HTTP 请求

这里我需要简单说一下这门技术，就是因为这门技术才能让我能写上软件，并且是有实质性用处的。有关这个介绍可以点击文章 [浅谈 HTTP](/blog/brief-talk-http/)

在开学军训一个月期间，也没有放弃学习易语言，不过那一个月应该不算学易语言这个语言，而是在学一个网络协议 HTTP，在这行的术语应该叫 POST 与 JS 逆向（基于易语言），这里我需要放一个我当时学习的链接 [零基础易语言 POST 入门到精通](https://www.zygx8.com/thread-7162-1-6.html)，导师教的非常好，是真心推荐，我从他的课程学到了非常多的知识，就凭我听了他的这一期课，就能自行写出超星刷课软件，我觉得这就足够我去报他的班。

### qq 机器人  

非常可惜之前写了一段时间的 qq 机器人代码无法使用了，相关文章 [纪念 QQ 机器人业黑暗的一天](https://www.bilibili.com/read/mobile/7009209?share_medium=iphone&share_plat=ios&share_source=QQ&share_tag=s_i&timestamp=1596387202&unique_k=vdFqN2)

我用的是酷 Q 框架，用易语言写的代码，花了也有半个月的时间去写![image-20200926164358137](https://img.kuizuo.me/20200926164358137.png)

那时候封装了好几个功能，最终就因为腾讯的封杀，导致自己辛辛苦苦写了半个月的代码灰飞烟灭。这时候的心情与当初 QQ 被永久冻结一样，不过现在也看淡了，就算回来说实话也高兴不到哪里去，也就在以前群在多吹吹牛逼罢了。（泽宇是我之前玩网的一个艺名）

这个 qq 机器人算是我手机端和电脑端一个变通的交互方式，以下是一些相关的菜单图，有些功能不方便展示，仅作为个人使用。

![image-20220517010245349](https://img.kuizuo.me/20220517010245349.png)

1. 对接网络验证服务端，购买卡密，实现购买卡密

![image-20200928204705890](https://img.kuizuo.me/20200928204705890.png)

2. 实现一些注册，例如一些软件注册给新用户多少时间使用，就不必在通过电脑，而是直接通过机器人发送命令来注册即可。
3. 群监控，监控群里的一些不良信息进行撤回，监控刷屏进行禁言操作。

只要你想，然后给上对应的代码就行了，那时候也是沉迷于 qq 机器人花了很多时间写这些接口。这里的话我说说那时候我用机器人来写一个学校的用手机进行超星线下考试。

事情是这样的，这个考试是**用手机考试**的，但只能带一部手机，同时老师提供了题库，**允许带资料**，差不多就是开卷考。而且用于考试的软件（超星学习通）是**不允许切换的后台**来进行搜题的（或者说切换到后台会扣分），有些手机是无法分屏的（但是有悬浮窗）。这时候该怎么办，难不成真的去把题库打印一遍？还真有，十几张来着，先不说好不好，找一题都要找半天，有没有更有效的办法，有，我那时候就是通过机器人。

首先，将老师发的题库，存入文件（那时候的我还不会数据库，就只好读文件），然后通过则匹配，将对应的题目，将答案全部都记录到数组里面去。接着在通过给机器人发送对应的命令如 查题+关键词即可搜到相关的题目。这里就放一个我当初录制的一个视频，（其他人操作也就是通过悬浮窗来）

![demo6](https://img.kuizuo.me/demo6.gif)

即便眼睛再也好，也比不过可靠的搜索，搜索可靠也不及关键词筛选，当初考试就是通过这样方式来通过这场用手机的考试，但是也有缺点，只能说当初写软意识不好，没考虑周全，像这个搜题我还要再打一遍【查题】这个关键词，很傻，而不是发送【搜题模式】，然后直接发送题目获取就行，再发送【退出搜题】（那时候花了一天时间去写）。并且对于这样的搜题还要切换特别麻烦，好一点的办法，有，自写安卓悬浮窗，不过现在也没这样的考试了，也是我后面学了点安卓后随手写的，悬浮窗大致如下。

![搜题悬浮窗](https://img.kuizuo.me/floating.png)

qq 机器人算是我特别想写的一个东西，但很可惜腾讯封杀外面大部分 qq 机器人框架，我使用的同样框架无疑避免，同时腾讯自己的机器人又不给开发者提供合适的开发接口，这就是腾讯吗，这本来就是腾讯的作风。

至于后续如果有时间，或者要发展 qq 群的话，肯定会重新再写一份 qq 机器人，到时候想要实现功能可就多了。

## 疫情期间，也是进步最快的时候

上一阶段学习期间，从 7 月到下半年 1 个月，这一阶段主要就是易语言与脚本开发，相关也就是上述了，而下一阶段，也就是从 1 月中旬到开学（5 月 23 号），也就是差不多这期间，开始了逆向初步学习和 Web 开发方面，而这段时间，可以说除了编程，就只有编程了。

先说下生物钟，晚上 6 点左右起来，然后早上 9 点左右睡觉，没错，这 4 个月基本上是这么熬过来的。（其中期间调整了两次作息习惯），因为疫情的因素，开不了学，又不方便出去，加上我本来也不喜欢出去，所以这阶段对我来说无疑是最好不过的，而这一阶段，也是我学习最多的时候，见识最多的时候，让我再一次感觉到编程的魅力，但同时让我感受到真正的编程和难。下面则会按时间顺序简单介绍下我学了什么。

### 资源共享吧

首先要提一个这个学习论坛，因为我在这个论坛上找到的很多教程，可以说没有这个论坛，我视频教程都不好找，先放个论坛链接 [资源共享吧](https://www.zygx8.com/forum.php)，首先这个论坛从名字上应该可以知道是资源共享的，是关于编程相关技术方面的资源，可不是那啥，我先放一张图片，看看到底都有啥资源。

![image-20201005025855844](https://img.kuizuo.me/20201005025855844.png)

别说，基本上有关编程的你在这都能看到，当然肯定不是免费了，是需要交 VIP 的，但只要 199 元，终身高级 VIP 会员，别提有多值了，你知道外面一套培训有多贵吗，这我就不提了，自己搜一搜就知道了，我在写超星刷课不是提过一个讲师，我报了它的班，4000 安卓 VIP+3000 网页逆向 VIP 来着，而这里你只需要 199 元，并且在该论坛你也能看到他的一些相关课程。当然，和培训相比还是有一定的区别，但在这里的教程真不差。

关于付费学习，可能有些人不解，为啥要收费，没为啥，就是你听付费的课程，能比别人学的快，能少走点坑路，很多免费课程要么就是为了推荐他的付费课程，要么就是为了推荐他写的书，总之，免费之中必有付费，单纯的免费课程能学，但想要走个捷径，付费应该是最快捷的方法。

是真心推荐这个论坛，一点广告费都没收，因为在这个论坛上我下载了特别多有关编程相关的知识，奈何时间不允许，不然我真的都学了。正是因为我在这个论坛上学习到特别多的知识，这就是我推荐的理由之一。

下面的大多数学习都是基于这个论坛上的视频教程。

### 安卓逆向

这上半年，我也只会网页端的数据分析与 JS 逆向，很多时候并没有网页版的，只有安卓应用，这时候想要偷其中的 api 接口，找到对应的加密点，该咋办，学呗。就必须要会安卓逆向，并且这个不比网页端简单。

在我开始提笔写的时候，已经有半年没怎么碰过安卓方面的了，我都快忘记了我安卓逆向的好多知识，而且当初还没有写笔记的习惯，就连我一开始怎么入门的都没什么印象了，总之就是看了教程，然后一步步照抄，视频教程怎做就怎么仿，就完事了。

同时也正是因为安卓，花了 10 天左右用 2.5 倍速度把毕向东的 java25 天速成教程看完了，而 java 才算是我真正第一门主流的编程语言，之前的 javascript 我是连 ES6 语法都不会的，甚至很多基本的语法我都不知道。但学完了 Java 的基础语法，但对于安卓逆向或者开发来说还是差太多了，虽说对于当时的我看的明白，但实际上整个安卓的项目结构我依旧不明白，不会点开发去搞逆向是真的折腾。

合理来说我安卓逆向压根就没学完，或者说我只学到了 java 层的源码分析（java 是真的好反编译了），我还没什么能拿的出手的东西，没有破解过安卓软件，只是分析了跟网页端差不多的 HTTP 请求，差不多的加密算法，在这方面我还真的不知道该说写什么，即使说了，很多没了解过安卓逆向人也不懂，后续的话会再学安卓这方面，从开发到逆向，到时候会这方面的知识在进行一个分类总结。（主要是我真的忘了太多了）

### Auto.js

我先简单介绍一下这个是什么，这个也就是专门针对安卓端的无 root 脚本操作，看到后缀名你应该能想到 js，正是用 JavaScript 作为脚本语言。可以说用这个开发工具也能开发些安卓软件，但主要还是针对脚本操作，比如做一个 qq 自动点赞的， 贴吧签到脚本，抖音自动刷视频，双十一用过淘宝叠过猫猫吧，用 Auto.js 也能写个自动浏览商品，刷金币的，此外有太多例子了。

在之前的脚本操作，我也只会电脑端的，而对手机端无奈只好投屏到电脑，通过电脑的鼠标操作来实现脚本，而现在有了这个软件，则就不用在连接电脑，直接将写好的脚本打包成安装包安装，点击运行即可。但对比原生安卓开发，这个开发工具还是略显下风，不过对于安卓的自动化操作已经足够了，我也只说说我用这个写了个什么软件。

#### 钉钉签到脚本

像抖音自动刷视频和贴吧签到这些我就不多举例，主要还是这个软件，听名字就知道是钉钉签到的，有些在疫情期间，学校老师又要求同学使用钉钉，并且签到，但是有的同学就是会忘记签到或者没起来（说的是我），怎么办，记旷课？这不写个脚挂在那边时间到了自动签到呗。

![ddqd1](https://img.kuizuo.me/ddqd1.png)

这是我当时写的页面，只需要填写对应的课程名和开始的时间即可，时间一到，手机自动亮屏，开始签到。主要的代码就下面这一个函数

```js
function ddSign(courseName) {
  launchApp('钉钉')
  waitForActivity('android.widget.FrameLayout')

  let course = text(courseName).findOne()
  if (course.parent() != null) {
    course.parent().parent().click()
  }

  let sign = text('群签到').findOne()
  if (sign.parent() != null) {
    sign.parent().parent().click()
  }
  sleep(3000)

  if (desc('群签到')) {
    sleep(3000)
    let btn_sign = className('android.view.View').desc('签到').findOne()
    let result = btn_sign.click()
    Log('签到结果' + result)
  } else {
    toastLog('不在群签到页面')
  }
}
```

启动钉钉，等待钉钉启动完毕，找到对应的课程名，点击课程名，找到群签到按钮，点击群签到按钮，进入群签到找签到按钮，点击签到，签到成功。就这么完事了，脚本就是这样的。

不过最终有个缺点，对于一些没有 root 的手机，需要每次运行就要不断的打开无障碍服务，特别繁琐，但没办法，这是安卓的机制问题。

这里要提及的一句是为啥不用 HTTP 发送请求要来签到，而是要这种脚本方式，对比一下你就会发现，用脚本写基本无压力，就是简单判断一些字或者图在哪，然后点击对应的坐标，而通过 HTTP 请求的话，一是要过钉钉登录，二是要处理各种加密算法。不过钉钉登录算法难不难我就不知道了，我也懒得分析，加上正好学了 Auto.js，索性就写一个这样的签到脚本得了。但说实话签到就不应该这样用这种定时脚本，而是应该选择协议更好。

### 深度学习之图像识别

可以去了解一些深度学习，颠覆我对机器的认知，至少让我又觉得编程的强大，重拾学下去的信心。首先，先看张图片

![QQ图片20201004030419](https://img.kuizuo.me/QQ%E5%9B%BE%E7%89%8720201004030419.png)

看图也能看明白，这个就是识别一个缺口的图片软件，可能对没接触过这行业的人觉得这并没有什么软用，这个滑块的意义主要还是防止人为操作和机器操作。对于人而已，自然而然知道缺口的位置，但是对于机器而言要怎么知道这个缺口的位置，就针对上面这类图片，可以通过图片颜色深度来定位到缺口的地方，同时也可以使用深度学习，简单来说深度学习就是 AI，不过这里的 AI 是用来让它识别这个缺口，至于怎么让它识别和对应的算法我就没过多了解了，我接触这个主要还是用现成的模型来训练识别的。说一下我是怎么让机器训练的。

这个过程其实就跟教小孩一样，现在有一个小孩，他不知道这个缺口的位置，这时候我告诉它缺口的位置在那，对应的操作也就是标注，如下

![image-20201004031417625](https://img.kuizuo.me/20201004031417625.png)

我把缺口的地方标注一下，并记录对应的坐标，然后告诉这个小孩，缺口是我标注的地方，你下次遇到的时候记得是缺口这样的，但是小孩毕竟是小孩，这是我换一张类似的图片，这时候他可能就蒙了，所以就需要不断的给他标注好的缺口图片，让这个小孩一直看，一直记，直到下一次看到一张陌生的图片，但是它已经把之前训练的给记住了，很快他就能找到缺口的位置，这整个过程其实就是告诉小孩，然后让小孩一直训练，这就达到了我们想要的目的，这个小孩也会知道缺口的位置了。现在把这个小孩换成机器，那么这就是深度学习，并且机器是机器，可以封装的“记忆”远比人类可比，人是会感到疲惫的，而机器不会。再比如下面这张图片

![image-20201004033445666](https://img.kuizuo.me/20201004033445666.png)

对于我们来说显示屏，键盘等等这些在常见不过了，深度学习就可以做到识别图片对应的物体分别是什么，不过这要的训练量就比上面那个滑块大多了。我这里简单说说主要的通途，现在我想拍一张人脸照，还有风景照，但是时间旧了，我想找可能就要一定的时间，这时候就可以通过识别图片，进行分类，例如头像照，风景照，食物照等等，现在大多数相册都有上述图片分类的功能，不止如此，通过训练还可以识别文字，快递单号，车牌号，识别人脸等等，总之想训练的东西，都能训练，不过就是吃训练量和显卡，一般来说都是用现成的模型直接用就完事了。对我目前而言，我深度学习学的是非常浅了，也只会用用模型，跑个显卡训练训练。

此外，既然图片训练是这样的，能不能训练其他的，当然，语音识别，模拟一些明星的声音，游戏 AI，甚至让机器自己去学习怎么打游戏，游戏内的人机也就如此，通过训练满分作文，实现写一篇条理清晰的作文等等，太多例子都能看到人工智能。

关于这方面我也不敢做过多讲述，毕竟也还没开始从事这方面的真正学习。但确实，惊艳到我了，也让我想去学习这方面的知识。想自己写一个属于自己的语音 AI 助手，训练出一个能自己玩小游戏的模型，总之想学的太多了。

### Chrome 扩展开发

关于这个的话可以查看我写过的一篇文章 [Chrome 插件开发](/blog/chrome-plugin-development)，我总结了一下我那时候学习的插件，和自写的一些模板，不在这做过多赘述了。

疫情这期间说句闭关学习应该不成问题，不过学的东西远不止于此，主要其他的没写出啥玩意，而且学的很浅很浅，比如汇编基础（不过应该已经忘得差不多了），又深入学习了编写游戏外挂，TCP 协议（和 HTTP 一样），还有就是一些开发工具的使用。期间也写过一些东西，例如疫情填表，网课签到等等，不过都是临时写来自用的，也懒得放图了，原理基本和超星刷课一样，有时间再整理一下。

## Web 开发

在进入了这个阶段，我就很久没再用易语言开发新的软件了。到目前为止也真的快半年没怎么动易语言的代码了（也快半年没怎么写项目了），就那个超星刷课而言，没想到半年后还能用。而这一阶段主要接触的也就是 Web 开发方面。

实际上，我在接触易语言就已经接触了一些 Web 方面，因为要针对网页的一些操作就不得不接触这些，比如 HTTP 协议，DOM 解析，CSS 选择器，JS。不过那时候这对真正的 Web 开发远远不够。当然要说一下为什么会想学习 Web，在学 Web 之前，实际上还接触了一些 js 高端操作，例如 AST 语法树（混淆 js 代码），WebSocket，但发现有点没学明白，用都有点没用明白，然后就是想搞明白这些操作，于是进阶学习 js。

其实决定性的也就是一门编程语言——JavaScript，那时候感觉到易语言的不足，恰好手头在学的一门编程也就是 JavaScript，于是就开始了 JavaScript 的学习，可以说不是 JavaScript，Web 想都没带想的。js 作为我目前的主力语言，就目前而言，基本屏幕上，必有 vscode 的痕迹，而且还是 js。js 吸引我的一个地方就是其他语言都能调用，加上语法清晰，不过要我硬说 js 哪里好，我也说不清楚，可能这就是对一件事物的热爱吧。

于是改变了当初原有的想法——搞安卓开发与深度学习，转行到 Web 开发去了，和学习其他技术一样，找教程，跟教程做一遍，自己在举一反三一波才不多就明了。

Web 学习确实比逆向轻松，并且学完 Web 能写的东西也算比较多。也算可以自己动手设计出一个界面或者功能出来，上手特别容易。这里我就不献丑了，之前前端写的那些代码真没脸丢出来。就连当初的笔记我都不敢看，写的是什么玩意，竟然还是在 js 文件上用注释来记录笔记。

就这样学习了几周，然后收到了开学通知，没错是开学通知，疫情的时候，开学一个月，于是就停滞了学习近一个月了，前面也说到，生物钟的问题，开学了就不得不强行调整生物钟，把我学习节奏给打乱了。其次突然开学，心态是真崩了，开学各种繁琐的事情搞得压根学不进去。原本不出意外在保持这样的学习状态一直学下去，暑假结束前就能把 Web 大部分知识都能搞完，然而却拖到现在。

不过也不能说开学不好，从 1 月 10 号放假，到 5 月 23 号开学，在家好像也就出门了 2 次，要是再这样拖到暑假估计身体可真要出问题。停滞了学习，但保住了身体，是赚是亏，对我来说血亏吧。

然而暑假由于学车的因素，加上外出玩了一波，整个暑假并没有像去年那样全身心投入去学习，不过业余时间会去摄取有关这行的知识与科普，也就是刷刷知乎，公众号，博客文章等等。而也就是在这段了解到了 Nodejs 与 Vue，暑假开始学习。

### Nodejs

不过学前端，Nodejs 基本是必接触，基本可以说只要是个前端开发者，就肯定会 Nodejs，关于 Nodejs 不做过多介绍，我能推荐的也只是一些 npm 包，总之在这期间 也就是只学了点 Nodejs 的基本使用，还有 npm (Node 包管理器)。

主要接触两个 Web 框架 Puppeteer 与 Express，关于这两个我在 Node 的那个目录内有简单说明到，这里也就不放链接了。这里简单说说 Express

### Express

学 node 大多数就是为了搞服务端，那么肯定也有对应的服务端框架，Express 就是其中之一。

在此之前，我是写过点后端的，也就是一个网络验证的系统，不过那时候是基于易语言和 HP-Socket 的库，但有一个很大缺陷，就是我这个后端服务器编译出来的是 exe 文件，也就是只能在 windows 上运行，这对 Linux 系统服务器很不友好，于是乎就想搞一个基于 Express 的网络验证服务端。

而这期间才算学会用 Mysql，之前易语言写的网络验证时连 Sql 语句都不会，还都是用易语言自带命令帮我封装好来进行增删改查。也简单的将易语言写的网络验证接口全都搬运至 Express 中，然而有个问题来了，我要怎么查看数据？难不成直接打开数据库查，怎么可能，而这就开始了 vue-element-admin 开发，后文会说到，因为这里要涉及到一个新的技能——Vue。

### Vue

可以说没有 Vue，我多半不会去继续学习前端（怎么感觉这句话好像说过了），和大多数人学 Web 一样，都是从基本的 HTML CSS JS 这前端三件套开始学起，然而在学到一半的时候，想写一写 Web 小项目的时候，就需要在一个 HTML 上写上大量代码，并且页面还不好设计，并且在用了 Express 一段时间后，发现如果要做动态网站的时候，就需要将数据渲染到对应的模板语言上（如 ejs），于是想知道有没有啥能解决这上述问题，于是便开始搜索有关前端的一些相关知识扩展，果不其然，困惑了我一段时间是否继续学习前端，因 Vue 而解惑。

如果要我评价一句 Vue，我只能说后悔学 Vue，后悔学晚了。我花了挺多时间在原生的前端开发，在了解到 Vue 时，还在用着 Jquery。Vue 让我继续学前端并不是数据交互方面，而是模块化，在我接触原生前端开发的时候，这个问题尤为困扰我，每次写一个页面都需要重复大量相同代码，即便是复制粘贴也觉得烦躁，而 Vue 组件化就不一样。例如我当前的博客页面，你会看到导航栏，侧边栏，与正文，评论系统，乃至打赏按钮都是一个个组件拼接而成，只需要在单个 Vue 文件中，引入对应的组件，然后想 html 标签那样直接写在视图层上即可显示，当然，相关代码也展示了，有关 Vue 的我也都放在 Vue 这个目录下。

然后就是基于 Vue 而开发的组件库，在 Web 开发难免不了页面设计，然而并非所有程序员又兼设计师，想要设计出一个好看的按钮或其他组件并非容易的事情，而组件库便提供了一个好看的组件供开发者使用，例如通过引入 element 的样式与组件库

```html
<!-- 引入样式 -->
<link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css" />
<!-- 引入组件库 -->
<script src="https://unpkg.com/element-ui/lib/index.js"></script>
```

然后在官网中找到需要的组件，如下按钮

<el-button type="primary" plain>主要按钮</el-button>

而对于的代码也就是

```html
<el-button type="primary" plain>主要按钮</el-button>
```

只需要简单修改一下参数就能生成相应的组件。哪里还需要花费大量时间去写 css，不断改参数，上手即用，快速成型。

Vue 模板语法也是，显示后端传递的数据也简单，比如下面这行

```html
<h1>标题: {{ title }}</h1>
```

只要接收到后端发送来的标题，即可渲染在`{{title}}`内，此外还有数据的双向绑定，关于这些等等不在这细说了，需要的话可以自行翻阅 Vue 文档。

而且就这么说吧，前端框架如果不学 Vue 和 React 其中之一，面试都不给你面试的。甚至可以说，Vue 和 React 是前端开发必学之一，不学就跟不学前端没两样。

同时 Vue 还是国人尤大大（尤雨溪）开发的，在国内生态好，社区活跃高。基本上目前国内的大部分 Web 项目都有 Vue 的身影，同时 Vue 不止于前端，小程序的开发等等也有它的身影，如 uniapp 就是使用 Vue.js 所开发的。

Vue 能做的太多了，如果你恰好准备学 Web 方面，尤其是前端，毫不犹豫，直接学 Vue，Vue 在 github 上排名第三，star 高达 175k，凭这，就足以去学习一番。

### Vuepress

Vuepress 一个 Vue 驱动的静态网站生成器，可以用 Vuepress 来写文档，写博客，而我目前这个博客便是通过 Vuepress 实现而成。关于这个博客搭建过程就不在这花费口舌了。我有一篇文章专门讲述这个博客的搭建过程。

### 对 Web 开发做个小总结

可以说没有前半年易语言学习所接触到的 Web 知识，就别说 Web 开发了，JS 估计都不会了解到，也因 JS，误打误撞闯进了 Web 开发的坑，让我领略到更多知识。

我算是作为 JS 语言的粉丝，听说过一句名言：凡是能用 JS 实现的最终都将用 JS 实现。即便不理解这句话的意思，但依旧这句话很喜欢，也许这就是对 JS 语言的一种爱吧。

![javascript](https://img.kuizuo.me/javascript.jpg)

写到这，其实 Web 开发真就要告一段落了，实在是不想在折腾 Web 了，主要就两点

- 不会设计，不知道该设计什么，实现什么功能
- 后端数据接口写的非常痛苦，Express 折磨了我一个月

在页面设计方面，有组件库很方便，但是不知道要写什么好，就那个网络验证而言，我已经绞尽脑汁想添加写功能，但是奈何后端写的折磨，很多我都写不下去，就比如用户-角色-权限 这三者关系，我只写了用户-角色 还有权限没写，还有积分，与 nodejs 模拟数据这些写的都特别鸡肋，实在是写不动了（甚至说实在不想写了）。想转型写点其他的，后续有时间才把这个在做过多优化。

目前把博客和网络验证基本搭建好，现在脑子只想好好写写博客，把这一年要学过的东西好好巩固一番，尤其是这一段 Vue 的学习，收获到太多知识，都有点来不及消化。后续会将这些都部署在该博客上（不妨点个收藏收藏个书签呗）

不过我目前的 Web 开发还有很多没学到，而关于 Web 后续的学习的话，以目前的话应该是不会在花费大量时间去学了，学了也有半年了，自我感觉 Web 这方面还是不适合我，但确实这半年的 Web 学习挺充实的，不会接着深入学习,会用已学过的知识去写一些东西出来。（主要还是太想学习其他技术了）

## 总结这一年学习经历

这一年学的确实多，相比于刚接触这行一年的人来说的话，我挺知足这一年能花费大量心思在这上面，同时还能坚持学下去，并爱上编程，于是乎从数学系转到了计算机系，但学还是自己学自己的，在我看来学校老师能教的，不如我课下自己学习的，况且编程单靠别人教是教不出什么玩意的，还真没听说过什么大神是另一个大神教出来了。

不过正是因为这种心态，也导致如今我连学校的课基本也都不会去上，能旷的课尽可能旷，旷课补觉，或者是宅在宿舍，坐在电脑前，盯着这赏心悦目的代码，和平时分相比它不香吗。既然旷课，那怎么能不挂科，虽然都是公共课，不过挺无所谓了。说件好玩事情，我大学英语挂了，没错，我英语说实在话就是不行，甚至我都没打算过四级考试，但这影响编写代码吗？不言而喻。

专业课的话，有的老师一般会认可我的能力，允许不去上课只考试就行，或者是不去也不会记名字，甚至和老师说一声不去就完事了。至少在我看来有时间在学校拿个文凭，不如直接用自己写过的项目去面试，至于看学历还是看能力，面试官一般都不看，因为只看简历呗。目前的话除了有关编程的事啥也不想干，估摸着在学一段时间就说不定真就辍学了呗，不，说不准还是给学校开除。

### 身体状态

早已习惯了凌晨晚睡晚起，早上的课基本也都是旷了的那种，就已经习惯了旷课。再看看下午的课，不出意外的话，一般就是坐在电脑前到，到点了吃个饭，接着到凌晨 3,4 点，每天反复这样。我都不敢说我在这方面有天赋，我只觉得我每天花费在这方面的时间是别人的几倍罢了。

**哪有什么天生，一切不过是摧残出来罢了。**

说实在话，挺享受整天在电脑前办公的生活（相比于躺在床上玩游戏而言），不过熬夜，久坐，与长时间盯着屏幕，甚至是掉发，身体难免有些遭不住，并且我是能感受到，身体一天不如一天了。但它改变了我原有枯燥的生活，成天的游戏与娱乐，迷茫到不知所措， 如果没有 QQ 冻结，还有易语言，JS 这些，说真的，我现在极有可能还在某个床角玩着游戏，还对着屏幕傻笑（现在就特别想笑）。即使都是宅在家的生活，但编程能成为我养家糊口的资本，而游戏，它不行！甚至还可能成为我颓废的资本。

### 事实告诉我，我还有很多要学

就在 9 月初，参加了一场网络安防（CTF）的比赛，真的是小巫见大巫，瞬间觉得自己还有很多要学的，虽然其他选手都很强，但也印衬了自己在这方面就是技不如人。说白就是自己菜，就是学的不够多，要是能在多学一点，说不定就多解一道题，多拿一点分，说不准就苟一波，吃个烂分。而事实是这是场没拿奖的比赛，而我是这场比赛的失败者。

不过有参与必有收获，确实让我学到了 Web 的一些渗透知识与一些工具的使用，也让我更加觉得外面的大佬是真的多，自己离他们还需要更加努力一把，这场比赛值了。

### 给别人的一些建议

我也仅能给别人一些我学习上的建议，也仅仅是当前，毕竟我接触这样也才一年多。我其实都不推荐去学编程这玩意，但我常常和别人说多花点时间在这上面，能写出些东西来。话是这么说，但真正肯去学的又有几个，即便学了坚持下去的又有几个，即便花费口舌去和他们介绍，但多数觉得难，但你说这个东西难吗，很难，但你说学不进去吗，也不至于，如今网络上生态这么好，学习编程的人越来越多，相互帮助的人也多，只要肯学，肯下功夫，随便一百度就有的结果，怎么可能学不会。

#### 多搜索，少问人

我这一年学习中，基本上就是看视频跟着视频教程一步一步照做，我一开始学习的时候也会在群里问过别人，也私下问过别人，但是有用吗？压根就没用，基本就没什么人会去鸟你的。百度它解决不了吗，如今强大搜索引擎会是你解决问题的最好选择（甚至我帮别人解决问题还是我将百度结果链接给别人）。有两句话，百度 5 分钟，问人 2 小时。百度能解决你百分之 99 的问题，但问人并不一定解决其中的百分之 1。

![006ARE9vgy1fwntelg0mlj30b40b4gm1](https://img.kuizuo.me/006ARE9vgy1fwntelg0mlj30b40b4gm1.jpg)

**没点自行解决问题的能力真心劝别碰这行。**

我现在都有点反感那些没自行尝试，就直接问人的那种，有什么好问的，要么你学的不够多看不懂呗，要么就是你那里少做了什么步骤，大不了重新卸载安装重启来一遍嘛。主要没点自行解决问题的能力，到时候项目一来，出了一个 bug，或者开发环境与生产环境有问题，你问谁去？问了会回复你吗？能不能先自行看看是不是哪里少改的，或者版本的问题。说太多就是想强调一点，自行解决很重要，决定你适不适合干这行。

话也不能说的这么绝对，有时候搜索引擎确实不能有效的给到你想要的答案，比如我所用过的一个博客主题，是有交流群的，但有时候我百度了半天就是得不到我想要的结果，然后一问群，**可能**会一些**热心伙伴**会给你**正确**的答案，但往往是在你**实在解决不了**的情况问。毕竟，问了不一定答，但是不问一定不答。

#### 复制粘贴也是一门技术

别觉得偷别人代码有什么丢脸了，有的人甚至连偷都不会偷，而你早早就把项目给搞定了，别人会认可你的过程还是认可你的结果，必定是结果好吧，而且干这行的，基本上你见不到一个程序员是真的每一行每一行开始写起，而用的最多的三个键 Ctrl C V，不会真有程序员一行一行的敲吧，不会吧，不会吧。

![7280a4701a0daf98](https://img.kuizuo.me/7280a4701a0daf98.jpg)

当然，上面也只是说笑了，只是现在都是模块化编程，什么意思呢，就比如我要实现一个自动发送邮件的功能，怎么办，直接搜索，比如 nodejs 中实现发送邮箱，于是就搜索到了 nodemailer 这个模块，二话不说，直接运行官方的 demo 案例，发现可以运行，能达到我想要的目的，接着就是自己简单的封装一下，直接给项目来使用，这不就成了吗？

然而要从 0 开始写一个发送邮件的服务端，别有这种想法，大概率不会想写的。就我目前而言吧，除了一开始学习新的技术外，会跟着敲一遍磨磨手感，到后面基本上都是从网络上偷各种源码，然后自己看一遍主要的地方是怎么实现的，然后对源码进行修改来达到我需要的目的。如今网络环境这么好，想要的功能随便一搜就有了，如果你能做到改别人的代码，那么别人这份代码你肯定也学进去了不少。为啥还要自己造轮子，尽可能利用别人已经写好的代码，而不是自己在手动写一份类似功能。

## 定一下明年的目标

到目前为止，我还有好多想写的东西没写，好多好多都写了一半由于一些某某原因没继续写下去。同时还有好多想学的技术，好多都是之前学到一半卡着就没接着学下去了。至于能学多少，尽力而为吧，希望明年的这个时候，在回头看看自己所写的一些代码，都能感到一些惬意。

### 进阶 Web 开发

Vue 才学了点皮毛，很多 HTML5 特性都没玩明白，CSS 也只会基本样式，就别说设计网页特效啥的，能看的过去就不错了，如果有时间的话一定会去接触写 React，毕竟和 Vue 一样，都属于人气爆棚的前端框架，很多大厂都使用 React 作为前端框架使用。

像 Webpack，Eslint 等等前端工具库，再如 JavaScript 的超集 Typescript，这些肯定都会去学习一番。

### 进阶 Python

学过点 Python，但并非精通，有特别多的库都还没用过。主要说说为什么想学 Python，最主要的就是深度学习这方面，在前面我也写过，深度识别之图象识别，其次就是模拟数据请求这些，如果知道 Python，说到爬虫自然不陌生，不过爬虫对我来说并不敢兴趣，主要是 Python 好做模拟请求，http 协议这些，在易语言这些操作都是乱写，基本没啥难度，当然，http 协议难得不是模拟数据请求，而是在逆向分析数据加密，风控算法，所以网页逆向的基本功还是要有的。

当然，如果有可能还会接触一些 Python 的后端框架 Djongo 和 Flask，看看会不会符合自己的编程风格，再考虑是否要重写一些后端接口，当然最主要的还是想学习 tensorflow，我本地环境都搭建好了，然而却没有花大量时间去在这方面学习！

### 安卓逆向与安卓开发

干这行前，我就是个重度手机使用者，或者说是玩机者。对于手机特别感兴趣，刷过机，拆修过手机，手头目前就有 5 台手机，对于电子设备可以说是又爱又恨了。用过的 APP 多，然而有些 APP 并不能达到我想要的目的，或者说有限制，于是就想尝试利用逆向技术去修改，奈何那时学了皮毛的安装逆向，根本不够用，也感到困难，于是就没继续学习，一开始的本心还是想搞安卓这方面。

如果可以的话，我更希望的是当一个安卓开发人员或者逆向分析工程师，而不是一个 Web 开发人员或者网络安全工程师。至于最后能如何，谁又能说的准呢。

### github

就目前而言，git 还有很多没玩明白，主要手头也没啥好开源的玩意，明年，一定多刷 github，当然最想的还是搞一个开源项目，还能在 github 上小有知名，那干这行真就无悔了。有点小野心，但以目前能力估计是够呛，想想就好，到时候技术上来的指不定就真成了呢。

这不才学了一年，才 20 出头，留给我的时间还多着很，在学个十年不成问题吧。但话是这么说，自己心里很清楚，已经有点学不进去了，就我写 Web 项目的这段时间，写着写着就去搞其他东西去了，实在是写的憋屈。总之已经没有一开始那么想学，那么有想法与灵感，那么肯于去学习新的技术，估摸着在学几年也许就会停滞于学习。

本以为年轻有的是精力，还曾妄想把好几门技术都学进去，但事实告诉我，能精通一个就不错了，学那么多意义又在哪，只是为了满足当初高中不认真学习的而又渴望学习的心吧。

总之，能学就尽量学，明年，理应更强。

## 感谢

最后还是要感谢互联网的前辈们，与当下互联网的环境，不然的话我还可能在迷茫中四处摸索人生的真正意图，耗费着年轻所带来的资本。往着曾经写过的代码，眼角不禁也会湿润起来（绝不是熬夜太困），感叹当初为啥会去学这东西呢，也许这就是编程所带来的魅力吧。

<p align="right">写于2020年11月18日    By 愧怍</p>
