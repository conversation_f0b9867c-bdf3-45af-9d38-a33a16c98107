---
slug: why-i-dont-write-notes
title: 距离我上篇笔记还是在?
date: 2023-03-13
authors: kuizuo
tags: [杂谈]
keywords: [杂谈]
---

当我起这个标题时，其实我已经很久没更新（翻看）过笔记了，甚至我都不记得我的博客还有笔记这个东西。

当我翻阅 git 记录，寻找上一次在笔记文件夹的 commit 提交记录，还是在去年的 10 月 1 号。

![image_U0EDw0PkAf1](https://img.kuizuo.me/U0EDw0PkAf1.png)

然而并不是我的技术栈没更新，而是我实实在在没去为这些技术栈编写过笔记。仅有的只是博文来记录自己所用的过程。

因此我想思考下我为何不记录笔记。

<!-- truncate -->

## 笔记的意义

首先不妨回答一下我所认为的笔记意义。

### 检索高效

当我需要回忆我曾经学习过的某个知识点时，笔记可以说是最直接有效的办法。与其再次使用搜索引擎搜寻答案，不妨直接从答案中找答案。

> 正如我笔记简介所述：**做到即查即用，能复制粘贴解决的，就绝不百度。**

### 巩固理解

相比绝大多数笔记内容都是初学者去记录自己所学，在这个阶段，你对知识的掌握程度是比较浅显的，而笔记无非能加快你的理解，同时也是是成本最低的，翻看自己的内容远比理解他人的内容来的简单。

## 学习方式

很多初学者除了通过视频教程来学习，当然不乏有些人是通过别人的学习经验（即笔记）来进行学习，包括我一开始也是通过刷别人的笔记来学习某个知识。

不过相对于视频而言，视频通常很啰嗦且时长感人（动辄可能数天的时长）。很多人不喜欢铺垫，不喜欢废话，就想知道某个知识点怎么用，结果如何。同时视频没有搜索功能，如果视频创作者没有对视频进行分集，当你需要回看的时候时，你往往需要从数十分钟的视频中不断的拖动，甚至到最后才发现原来我看的不是这个视频。

而文档则不会，那你可以通过 Ctrl + F 搜索你想要的关键字，相比视频而言，检索的效率将会大大提升。

这也是我为何从视频学习转成文档学习很重要的原因。我也很推荐如今的学习者从文档学习，而不是视频学习。

## 为何不写笔记

### 笔记应该记录哪些知识点？

笔记无疑是耗时的，你可能会花费数个小时的时间，去总结一个可能你职业生涯中都用不到几次的知识。

为什么这么说，因为我发现我有很多笔记就是这种情况，你也可以回想一下你所记录的笔记，有多少是经常使用到的。

尤其是当你使用的足够多的情况下，你甚至都无需翻阅笔记。脑海中自然就会复现出你需要的东西，此时为何还需要看笔记。为了验证脑海中的正确性吗？

想想看你会为怎样的知识点大费周章的记录，是一个自己都不怎么用的知识点，还是用到滚瓜烂熟的知识点？

当我们经常使用某些知识点时，自然而然就会记住它们，这时笔记就没有太大的意义了；而对于使用频率较低的知识点，记录下来的意义也不是很大。

### 搜索大于记录

究其原因，还是因为我翻看笔记的频率变少了许多。如今的搜索引擎很智能，只要你用的不是百度，就能很快速的搜寻到答案，为何还需要记录一下，换成自己熟悉的口吻，到最后就变成上面所说的那样。而在 ChatGPT ，new bing 的诞生下，更加剧了我这一行为。

**当搜索大于记录时，笔记就显得弱不禁风。**

### 官方文档与学习笔记

说实话，当你看到别人的笔记的时候，你会认真的从头到尾看一遍吗？我想不会，更多情况我们只利用搜索功能去获取我们想要的知识点。

但如果此时有两份文档，一份是官方的技术文档，一份是他人的学习笔记文档。你会选择哪一份？是我肯定毫不犹豫的选择前者，他人的学习笔记并不会及时更新，但官方文档只要还在维护，那么必定处于常更新的状态。假设我照着他人的学习笔记学习，此时正好有个函数的使用方法更新了，那么我必定会踩坑，导致不必要的 bug。而官方文档则不会，官方一旦有破坏性更新，通常会有显眼的提示，和 changelog 可供我参考，这就能及时有效的帮我排除不必要的坑。

这也是我为什么会宁愿去看官方文档，哪怕是英文的，也绝不愿去看第三方中文翻译后的文档，我有太多的坑就是因为更新不及时，存在信息差导致的。所以无论如何，能获取一手的信息，就别用二手的，甚至你也不知道人家会加工成什么样子。

## 最后

综上，我想我已经把我为何不写笔记的原因讲述的比较清楚了。

当然我并不是说笔记就一无是处，因为每个人，每个阶段的学习方式不同。曾经我也是笔记学习的追崇者，但如今的我能够不借助视频教程，不借助笔记来进行独立学习，所以为何不选择一个对我而言更高效的方式。
