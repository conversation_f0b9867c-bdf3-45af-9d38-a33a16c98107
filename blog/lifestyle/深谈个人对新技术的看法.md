---
slug: talk-new-technologies-opinion
title: 深谈个人对新技术的看法
date: 2022-10-15
authors: kuizuo
tags: [杂谈]
keywords: [杂谈]
---

目前技术圈的发展速度可谓是有目共睹，尤其是前端，每隔一段时间就出新的技术，可以说让很多初学者非常畏惧，没有一个明确的方向不知道从何学起。

同时也有很多人，只局限于使用手头已掌握的技术，而不愿去尝试新技术。举个例子，如今 vue3 都已经正式发布，但仍还有停留在 vue2 不愿尝试 vue3 的开发者。而 java 都发布 18 版本了，可还有很多人都还使用着 java8，这种现象可以说是非常常见了。

这里说说我个人编码经验与看法，**仅作为个人观点，没别的意思**。

<!-- truncate -->

熟悉我的人应该都知道，我对很多新鲜的技术有一种难以用言语表达的情感，又哭又笑。属于是那种看到感兴趣的就会开始尝试，在之前也许更强烈。

驱使我去接触的原因无非就以下几点：

- 开发体验、性能提升
- 对已有技术的厌倦感和对新鲜事物的好奇心
- 更多机会与方向
- 对未来技术趋势有更好的了解

## 写不完的代码

首先要知道一点，在任何的软件开发迭代中都没有最终形态的代码。说白话就是代码都是不断更新的，永远写不出最好的代码。

你能看到如今很多开源项目或者商业项目都在不断新增代码或者功能，除非作者不维护了，不然这份代码可以说写到世界末日。

究其原因还是因为社会不断在发展，硬件升级，性能提升，不断的业务需求。毕竟人都在进步，社会难道还不能进步。所以必然会有新的技术出现，只是出现的时间快慢，与技术难点突破。

## 开发体验、性能提升

毋庸置疑，在购买方面，肯定是买新不买旧，同样的在技术（软件更新）方面也是则从用新不用旧。驱使软件和框架更新的原因也就是新增了某些功能（特性），对用户（开发者）的体验有所提升。一般而言比较少的会存在反向更新的操作，这里除了某些国产软件~~（如某信，某 Q 等等）~~

就我对此的看法也是如此，假设一个开发工具启动花费了 5s 钟启动，而在它的最新版只需要 1s 钟就能够启动，你会选择更新尝试吗？。再比如一个框架原先的代码需要 10 行代码才能实现的功能，由于新版本提供一个语言级别的语法糖，使该功能只需一行。

并且我对开发体验非常在意，尤其是不好用，或者不好配置的东西，我基本秉持能不用就不用的原则，像 vue2 与 webpack 就是这样，我跟愿意使用 vue3 或 vite。诸如此类的替换有非常多，便不一一列举。

要我肯定毫不犹豫的更新去使用，但有些人可能对此提升不是很在意，又或者是升级的成本相对较高，也可能是因为这个开发工具（框架）他用的比较少，更新的意义自然就不大。

## 对代码厌倦和对新事物的好奇心

我写代码时常处于三分钟热度的状态，有些东西可能也就一开始的时候感觉比较新奇，然后就不了了之了。我对此的看法主要还是容易对代码产生厌倦感，不想写代码，不愿意学习。当厌倦感产生了，自然而然就放弃编写，也就是三分钟热度的状态。不过也能侧重体验一点，那就是肯定我对此不是那么感兴趣，既然不是那么感兴趣的东西，又何必在写下去呢？

接着过段时间又遇到了一个新的技术，冲击了我的好奇心，开始尝试。 如此重复，就会发现啥都学了一点，但实际是啥都没学到。但至少，让我肯去学习，而不是在原地踏步。而从心底里就想接触的新鲜事物，每次接触到就能满足自己内心的好奇心，就这一点我便知足了。

### 重构的艺术

如果回头看看自己 1，2 年前写的代码，会发现原来自己也曾写过丑陋不堪的代码，也成为过自己所讨厌的样子。如果这份代码我将来还会用到，那么我就尝试去重构，也许在当时还不支持某种特性，代码就无法简化。基于现有的水平，便会发现很多代码都有可改善的地方，可以化繁为简。代码重构属于将杂乱摆放的东西，收拾的整整齐齐的样子。重构是在提升观赏度和舒适度的同时，还减少 bug 的诱发概率。

> 在编程语言级别水平上，也就是我为什么会想去使用最新的版本（ES2022，TypeScript 4.9），即便是兼容性的问题，我也会去使用，就是因为能够满足我对代码的舒适度，这便足以。

### 生态与解决问题方面

我是很感谢新技术的出现，他实实在在的解决了一些我已有的痛点，提升了我的开发体验。当然它也让我踩了无数的坑，也折磨过我。但不可否认的是，我的自我解决问题的能力也在不断提升，如果我学的是一门比较流行的技术，那么我所遇到的问题，很有可能别人也遇到过，并将他的解决方案分享出来。而我就很容易根据报错描述找他的解决方案来解决我的问题。但在新技术下，用的人自然而然就少了，所分享的问题解决方案也就少了，所以在这种环境下，我就需要自行翻看源码，查阅文档，提出 issues 才能够解决问题。自然而然解决问题的能力也就有所提升。

像流行框架能有这么有问题解决方案，就是因为强大的生态，同时这也是生态好处之一。

> 因此也有很多人顾忌使用新技术，就是因为遇到问题不知如何解决。包括我也是，但通常我会观察一段时间，等成熟了我才去尝试，而不是直接上手，避免踩一些不必要且耗时的坑。

### 总是活在舒适圈

在圈内有着熟悉的环境，与认识的人相处，做自己会做的事，所以会感到很轻松、很自在。但是当踏出这个圈子的界限的时候，就马上会面对不熟悉的变化与挑战，因而感到不舒适，很自然的想要退回到舒适圈内。

我在阅读英文博客的时候，我也时常感到不舒适，阅读不下去。我也很想回到舒适圈，使用翻译软件来翻译但是这样就会导致我非常依赖翻译软件，就间接失去了一次英文环境与英语能力的提升。

长时间待在舒适圈，会让自己过得很舒服，但是却很难提升自己。不过想想也是，**提升的过程不就是苦尽甘来**。

以目前来看有一种这样的学习趋势，别人学什么，当下什么技术火，就去学什么。我其实特别反感这种现象，也不推崇这种学习理念。我会做出我的解释：

首先，什么技术会火就学什么，这固然没什么问题，如果一门技术没有热度，没有生态，那么学了的意义不大，一是难有长久稳定的技术发展，二是不能将技术变现。而绝大多数人之所以选择火的技术，有很大一点是因为有前人给他铺了很多“路”，如学习指南，思维导图，视频教程仿佛跟着学就能成为编程大牛似的。可一旦没有这些，就不知道该如何下手。始终都是跟着别人步伐学习，思维很难扩散出去。

并且这种现象必然会导致内卷，首先看看国内的技术，Vue 和 Spring boot 的可以说 10 个 web 开发程序员中有 8 个技术栈是这套，比麻花还是卷了，可薪资呢？

这里我并不想贴相关的薪资图片，你完全可以自行去了解，但是我可以肯定且直接告诉你，React 的薪资普遍会比 Vue 高上一截，而 Java 后端开发，如果技术只停留在 CRUD 的层面，工资普遍也高不了多少。

**如果你不去拓展自身的技术栈，不多去了解一些未来的可能会火的技术，还停留在当下，活在舒适圈。那么薪资大概率不变，并且自身会有很大被劝退的概率。**

与时俱进，这是我认为不断学习新技术，提升自身技术栈，非常重要的一点。**过得舒服，反而过得难受**

## 更多的机会与方向

技术更新迭代越来，也带来越多的机会，这对于接触前端的我感到尤为明显。假设当下又出了某某技术，那么必然会引起软件开发者的关注，于此同时就带来了维护者，贡献者，甚至是一些金主投资商。像 [Tailwind CSS](https://tailwindcss.com) 与 [Vercel](https://vercel.com/) 就是一个很好的例子，两个前端明星项目，有兴趣可以了解它们的故事。

**不过这种机会在国内不太多见，反而在国外特别普遍。**

但必须要承认的一个事实，如今技术发展过于迅猛，加上目前就业行情不容乐观，当别人了解过的东西，你却不了解，那么别人所能遇到的机会自然就比你多。说的难听点也就是没有对技术提升的想法，今后项目迭代的过程中使用到一些前沿技术就难以胜任。

### 没有目标的学习，等同于乱学

**没有一个明确的目标，学任何（新）技术都是乱学，充其量也就只比不学好一点。**

这在我初学阶段尤为明显，我一开始也不清楚我以后会从事什么行业，可以说是什么都乱学一顿。在我的一篇年终总结 [2019.7-2020.7 编程年记](/blog/2020-year-end-summary) 中可以说是尤为明显，尤其是在 [定一下明年的目标](/blog/2020-year-end-summary#定一下明年的目标) 的段落中，最后我真正深入学习的也就是只有 Web 开发。

我相信很多初学者也会遇到类似的问题，不知道学什么，想学好找工作的但是薪资不高，想学感兴趣的但又不知如何下手。说实话，要我回到当初，我也难以抉择。也有可能处于摆烂状态或是乱学一同，到头来啥都会一点点，但是又好像啥也不会的样子。

这里我是奉劝先定一个短期的目标，为了这个目标我要去学习哪些技术知识。这里就说我未来一年的目标为例：我未来一年想写开源项目，为开源社区做一份微薄贡献，乃至从事开源行业。那么我就需要了解写开源我需要那些预备知识，例如 Github 的使用，项目规范，英文交流等对应开源项目的技能知识，这才是我所该学的，并且能够实实在在用到的，且对我未来有用的。

## 对未来技术趋势有更好的了解

**当你了解的技术越多，你就越能知道自己适合哪些技术。**并且当你去尝试过后，更能加深你对某个技术的信仰。

在未来技术只会越来越多，因为当下要解决太多问题，有太多的业务需求开发。只要不断有需求，就不断会有技术更新。但技术更新必然是朝着好的方向去发展，即技术趋势方向。而了解的越多，能看到技术趋势也就更远，方向就更难偏移。

像我目前就比较看好未来 js/ts 的发展，这也是使我从逆向和爬虫转到 Web 开发行业上，并且将会长期发展下去。

但很多程序员就缺乏这种对**技术的认可**，甚至眼光比较浅薄，认为自己当下所学的就足以，可没却从未到真正的”外面”去看过。

当有了对未来技术趋势的了解，自身就有相对明确的目标学习，而不是漫无目的学习，跟风学习。

## 我是如何了解到这些技术的？

也许有些人并不在意新技术是否学习，而是好奇我是如何知道这些技术的。这个问题非常好，我自己简单总结通过那些途径来获取到这些相关技术的新闻。

主要有以下几种来源：

- 多加技术群，不定时看群聊
- **多刷技术大佬文章（推特），或者是技术公众号和掘金（最多的也是最有效的）**
- 订阅一些技术周刊，或订阅某个项目

没啥技巧，就靠刷技术文章，自然而然的了解也就越来越多了。

尤其是第二点，也是我了解这些新技术的最直接途径。与其自己去主动了解新的技术，将刷抖音的时间改成刷技术文章，了解新技术就是分分钟的事情。可以说我写博客是因为这个契机，记录自己用到的技术的开发过程，并分享个人的开发体验，让更多人了解到这些新技术。

## 面对新技术该怎么学？

其实更多时候是比较在意如何去学一门新技术，而不是找一门新技术，当阅历多了，技术自然就了解的多。这里我分享下我对于新技术是如何起步与学习的。

首先我会列举出我近期感兴趣的技术，这一步很关键，我当然不可能每个技术都去尝试一遍，时间精力根本不够。通常在我了解到这个技术的时候，比如文章与视频中，都会介绍到这个的优点与用法，这就足以了。

但想要进一步的学习，还是得依靠实战项目（至少我都是通过实战项目来学习的），这时候我会看看手头的项目，看看有没有能够基于上面所列举出的新技术升级的想法，如果有的话，那正好就当重构与新技术的学习，这是最好的，也是相对比较节省时间的。

但如果没有的话，我通常是会考虑另写一个项目，而这个项目可能是某个灵感的实现，也可能是久违想写的项目，或者是复刻某个感兴趣的站点，总之从上面所列举出来的技术中去选择一个来进行实践。在项目实践中去尝试使用这些新技术，哪怕只是实现一个简单的 demo，也总比单纯的刷文档，看代码来的有效。

**在项目实践中学习，永远是最直接也是最有效的**。回想你编写课设或者工作的项目，是不是在项目开发中进步的最快？如果这时候还有点时间紧迫感，进步反而会更快。（当然焦虑和压力也会随之提升）

## 最后感悟

关于本文，必然有引来一些不同看法与见解，每个人都有对不同事物的理解，我只是将我对新技术的看法，以文章的方式输出出来。本文并未带有任何的技术的偏见，我对任何技术都保持一视同仁，并且愿意去尝试学习。

不必抱怨新技术发展的过快，自己来不及学，学不完。或者担心自己学的东西在未来将会淘汰，等同于白学。学习过程就是一个非常好的经验总结，当你回顾整个学习过程，其实都没有白学。反而多一次的学习过程，在未来学习新的东西时，学习的成效也会显著提升。保持不断学习，就永远来的及学习。

更多时候，不应该只学如何使用，而是该想想这东西是在什么样的契机下如何被创造出来的，解决了什么问题。而这个问题在未来有没有什么更好的解决方案可替代，如果有更好的解决方案，那么必将又将发展出新的技术来更好的解决这个问题。这在我曾经的学习中，我是从未考虑到的，只专注于学习，而没去了解为什么。

我是希望越来越多的新技术出现，无论它是为了解决什么，必然能解决某些人的一些需求，那么它的出现就很有意义。至于未来该技术和相关生态发展如何，不得而知。也没人敢笃定未来这个技术必定会火，就去学这门技术。绝大大多情况下都是比较看好这门技术，认为未来可期，同时又感兴趣，就开始学习并使用。
