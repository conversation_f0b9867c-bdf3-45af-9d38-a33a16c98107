---
slug: easy-language
title: 谈易语言
date: 2020-10-08
authors: kuizuo
tags: [杂谈, 易语言]
keywords: [杂谈, 易语言]
description: 谈谈易语言其优缺点以及我对易语言的看法
---

好歹自己学习易语言也快有一年了，也用易语言写了一些软件，特此记录一下以及对易语言的个人看法。

该文章有可能过于啰嗦，可吐槽的点太多了，也正有感悟才能写的这么多。

:::note 补

2021 年 3 月是我最后一次打开易语言，至此我已经很久不写 exe 项目了。

:::

<!-- truncate -->

## 易语言介绍

还是简单介绍一些易语言吧，毕竟肯定有很多即使学过编程也没听过易语言的，易语言是一门以**中文**作为程序代码编程语言，简称 E 语言(EPL)，创始人[吴涛](https://www.baike.com/wikiid/3464184696167845391?view_id=2nxgpp3bv6k000)，2000 年一个人独立开发易语言。

有关易语言的特点如下：

### 易语言不开源

不像主流的编程语言 C，Java，Python 等是开源的，易语言是一款纯正的商业编程软件，易语言正版加密狗 618 元，不过有破解版，不然多数人都不会去接触易语言了。但不开源就已经注定了易语言的在整个生态就不行，并且易语言已不在维护了，也就是很久很久没更新过，或者说不会再更新了，作者也已不再管易语言了，目前也就一些易友去开发一些相关的插件模块库这些。

### 全中文界面，可视化 UI，填表式的声明

我这里放几张图展示一下

![image-20200914010759872](https://img.kuizuo.me/20200914010759872.png)

![image-20200914011112087](https://img.kuizuo.me/20200914011112087.png)

首先要吐槽一下，2000 年的页面与 2020 年的页面可以说是完全一模一样的。开发界面是真的丑，但有一点是，页面的语言命令都是全中文的，比如`if`所对应的的就是`如果`，`MessageBox`所对的就是`信息框`，很多命令都中文化就再举例了，并且每个函数都是以表格似的填写，也就是代码的格式都定死死的了，如果你学过其他的编程语言在来和易语言比对，你多半会学的够呛，很难理解为啥要这样。不过也正是填表式声明，导致易语言过于简单，后文也会提及。

在比对一些 C#的开发界面

![image-20200924125428023](https://img.kuizuo.me/20200924135324.png)

![image-20200924125907216](https://img.kuizuo.me/20200924135325.png)

可以看到页面肯定比易语言好看 100 倍，但是随之而来的就是难度的提升，先不说好写与不好写，你让一个没学过编程的看，多半看的云里雾里，这时候就会劝退很多人瞬间不想学了，相信很多学编程的都有这样的经历。

#### 上手容易，可以做到极速开发

接着再来说一下上手学习，正是由于有上面那个前提，易语言可以做到上手特别快，可以说会用电脑，有逻辑，会识中文，易语言好学的一批，基本上学个几天自行写个软件完全没问题。对于国人一点编程基础都没有的新手，并且英语还不好的话来说，易语言可能是真的好上手，我当初学易就有一部分就是给英文劝退了。

首先我要提的是可视化界面设计，你只需要将旁边的组件拖拽至窗口页面上即可，相对于的属性，例如内容，宽高，颜色在旁边显而易见，要修改只需要点击修改对应的数值即可，而对于其他的 IDE 来说，如果英文不咋好，并且还是第一次用，找可能都要找几分钟。而正是这个可视化界面，让我当初有信心学下去易语言，如果你学过 C 或者其他编程语言，一开始都是在那黑不溜秋的控制台显示，我就只是想写个软件用用，你给我讲那么多理论知识，甚至我还听不懂的那种有个嘚用。

同时还可以直接打包成 exe 文件，直接在 windows 上运行，发给别人也能运行，哇，瞬间感觉到写软件的牛逼之处了，直接小有成就一波。这里我放几张我当初学易语言写的一些界面吧：

例如写一个骗骗小学生的 2020 年最新刷 Q 币软件（用到了浏览器的填表功能改了 q 币的值）

![demo](https://img.kuizuo.me/20200924135326.gif)

在比如做一个音乐播放器（是有声音的，只是我录制的是 gif）

![demo1](https://img.kuizuo.me/20200927031909.gif)

在比如一些自动添加好友的

![image-20200924191526221](https://img.kuizuo.me/20200927031910.png)

在比如写一个注册机模板

![image-20200924192403210](https://img.kuizuo.me/20200927031911.png)

网络验证

![image-20210819233054879](https://img.kuizuo.me/20210819233054879.png)

![image-20210819232928124](https://img.kuizuo.me/20210819232928124.png)

还有特别特别多的例子我就不举例了，这些用其他的编程语言肯定能写，但是与之对应的就是学习成本，很多人学其他编程语言，甚至还没学到界面设计就开始放弃了，原因很简单，没兴趣学呗，易语言界面好设计，但是基本都是原生 windows 组件，对于新手来说这完全足够设计出自己的软件了。

**要是没能在最想学习的时候，满足自我的成就感，那很有可能就会学不下去**。

我当初学易语言也是这样的，暑假学了两个月，其中第一个月学基础到还没什么，也就开始学习易语言的基本语法和编写一些程序来玩玩，但这些说实话没什么可看的，或者说没什么可用的，就想上面那个骗骗小学生的刷 q 币软件有用吗，没用呗。初学的一个月就都开始写这些可以说毫无软用的东西，直到我照着视频一个字一个字的模仿着敲一遍扫雷一键秒杀的代码，没错，就是这个激起了我对编程，让我感受到编程的魅力。放上一张 gif 图片。

![demo2](https://img.kuizuo.me/20200927031912.gif)

当初照着视频一步一步来最终完成了该软件，但那时候的我其实根本不知道为什么可以这样，直到后续了解到汇编与游戏内存相关的知识，我才算真正懂的当初扫雷外挂的原理。

也正是因这个扫雷的外挂，让我接下来的几个天疯狂的学习，去写其他的游戏外挂，比如连连看的一键秒杀，消消乐，植物大战僵尸等等。这里我也放一张图吧（还特意去下载 qq 游戏）

![demo3](https://img.kuizuo.me/20200927031913.gif)

不过后面就没怎么学习游戏外挂相关的，一是所看的教程是 11 年的，中途没更新了，二是目前热门游戏以我目前能力写不出来，只要加上了检测，就过不了，并且容易封号（注：我 QQ 可不是开外挂给搞封了，就算开外挂最多也只是封游戏账号），最近接触的也就是 CF 越南服的外挂，有教程于是就学了点皮毛，不过教程又教到一半，就没深入去学习。这里提醒一句，写游戏外挂并销售是可是会给抓的。

不过有点扯远了，就凭这一手界面设计，易语言其实就足以容易上手写出个软件出来。在叙述几点易语言容易上手的地方，自带提示，全中文文档，比如下图

![image-20200924220413124](https://img.kuizuo.me/20200927031914.png)

只要你鼠标选到对应的函数上，按下 F1 或者点击提示，就有对应的函数提示，对应其他语言也有，但是纯英文的，门槛就高一个档次。

#### 精易模块

如果没有这个模块也就易语言跟其他语言的区别可能就是一个是中文一个是英文了。我就举我用的最多的一个命令`文本_取出中间文本`

![image-20200924220908502](https://img.kuizuo.me/20200927031915.png)

而对于其他的编程语言，这类语言还需要自行编写一个函数来调用，而精易模块则是直接封装好好的供你使用。你都没必要去了解底层的函数，直接把门槛降了一个大档次。

至于相关的程序编写我也不多概述，下面就是易语言的缺点。

### 易语言的缺点

我说说我用易语言的缺点，也是我最不推荐别人学易语言的了

我上面也说到过 2000 年的页面与 2020 年的页面可以说是完全一模一样的。虽然有易友开发了仿 VS 界面的，但启动起来影响运行速度，我就没安装了。虽说我不是强迫症，但用多了 vscode 与其他的 IDE 相比，看到易语言就能想到是几年前的软件了。

#### 占用空间与运行

易语言毕竟还是一种封装过的语言，带来的方便，同时也牺牲了性能空间，与原生的桌面级开发相比易语言是无法比的。就比如用 C#开发的所占用空间肯定比易语言少，相关的性能优化更好，这里我就不放图了。

#### 软件报毒，即使没毒也会给杀毒软件报

这里我有必要说说关于易语言的一段故事，这里我放几个链接，可以去了解一番 [刷枪改图强登游戏 CF 外挂](https://www.bilibili.com/video/BV1WE411E7mQ/?spm_id_from=333.788.videocard.0)

[为什么多数外挂都用易语言？](https://www.zhihu.com/question/20690643?sort=created)

如果你在 2010 年左右接触过网络游戏，你肯定遇到过各种各样的外挂，而这类外挂多数都是出自易语言之手，甚至你现在在外面遇到的很多游戏脚本外挂，易语言也能占据多数。你随便百度一些易语言，相关的评论都是有关外挂这些。但事实上你只会易语言是根本不够写外挂的，我学过相关外挂制作，虽然学的浅，但至少学过。是需要汇编这类基础，但又为什么会很多外挂是用易语言写,，而且都是些水平不是特别高的人，原因很简单，因为那些写挂的很多都不会真正写挂，只是调用别人封装好了的库，甚至就连易语言自身都带了外挂库这些。让他们写一款新游戏的外挂，他们多半是写不出来了，原因就是他们不懂汇编这些，但是调用写好的库就 6 的飞起。当然这其中还是有些利益相关的方面，我也不多提了。

如我上面所的我一个初中同学，要不是我接触了编写外挂这些，我还真信了他当初能写的，实际上都是修改外面的源码，或者是直接调用写好的库，直接偷源码用。

因为外挂行业的崛起，导致一些厂商不得不进行一定的处理。总之，目前易语言写过的项目，多数是会报毒的，即便没毒，也已经给杀毒软件的厂商给拉入黑名单了，所以可以说没公司要易语言的程序员，即使软件没毒，但是还是报毒，你是信杀毒软件还是易语言？

#### 说说我用到的一些坑

我在做一些网页数据获取的时候，竟然连个 DOM 对象都没有提供，当时没接触前端，不知道有 DOM 对象，还是用正则去匹配，那时候是真的 nc。接触了前端，发现易语言竟然没提供 DOM 对象操作，我还是用别人封装的 DOM 类，并且还有可能出现匹配不到情况。

其次是调试的时候，对于变量值长度过长竟然无法直接查看，还需要保存为文本才能查看，并且我调试的时候常常崩溃，导致我每次找一个 bug 的时候都需要重启易语言好几遍才行。

由于是类似表格式的填写变量，参数与类型，也就导致了无法在其他编辑器上进行编写易语言代码比如我复制一个函数，给我的结果是

```
.版本 2

.子程序 子程序1, 整数型
.参数 参数1, 文本型
.局部变量 变量1, 整数型

变量1 ＝ 到整数 (参数1)
返回 (变量1)
```

而在易语言所对的是

![image-20200924222319462](https://img.kuizuo.me/20200927031916.png)

在易语言中的引号`""`，只能通过常量`#引号`，或者通过常量表，就比如下面这个 jsoin 字符串 `{"a"="123","b"="321"}`，而易语言的写法就是，`"{”+#引号+“a”+#引号+“=”+#引号+“123”+#引号+“,”+#引号+“b”+#引号+“=”+#引号+“321”+#引号+“}"`，一个个通过字符串来拼接，巨麻烦，也是我最想吐槽易语言的，不过也可以通过常量表来替换，但依旧很麻烦。

还有易语言自身是不支持 utf-8 编码显示，原因很简单，当初只是为了给国人用，gbk 显然是更好的选择。但有时需要 utf-8 的，这时候就莫得办法。

#### 没公司要易语言程序员

几乎没有公司招聘易语言程序员，实际上上面所说的就足以证明易语言不行了。并且很多人都不看好易语言，黑易语言，至于为什么黑，百度或者知乎想必会有更好的答案，这里我也就不再赘述了。

### 小总结

写到这，我其实有点想把介绍易语言的一部分给删了， 我不推荐新手去学易语言，因为易语言相比于其他语言，它还是太弱了。但如果没有这半年的易语言学习，让我天真的以为编程的简单，又怎么鼓舞真正入坑，让我去学习更多更深奥的知识。

不过就目前而已，我已经很少用易语言写东西了，但如果要写桌面级软件，我还是会首选易语言（因为只会易语言），毕竟写了也有半年了，开发效率也高。如果有机会的话，会深入学一下 C#还有 Qt，不过也不知道是什么时候才会有机会。

关于英语方面的话，我是挺惧怕英语的，我高中英语就没怎么及格过，甚至我大学英语还挂科了，但易语言给我带来了编程希望。就我目前学习来说，编程还真的不怎么吃英语，看不懂英语文档，翻译成中文文档不就完事，而且学多了就会发现太多都是死代码，需要的时候翻阅文档直接 Ctrl C V 使用即可。英语对编程来说只是为辅，英语好并不能提高编程的上限，同时也决定不了下限，就这么说吧，你让一个学英语专业的人来看一份几百行的代码，基本注释写得在详细，他没学过编程，能看的懂吗？但有很多开源的项目都是英文的，会英文固然是好，但不会就不行了吗，看不懂英文文档，我翻译还不行吗？说这些，就是希望别用自己的短处来阻劝自己的目标，很多时候都是学了才知道这个有没有用，没学有锤子用！

能接触易语言的，多半是没什么英文基础、编程基础。不能说易语言适合新手的入门语言，但绝对是能让中文普通用户眼前一亮，产生编程好感的一门语言。

易语言是我接触过的第一款编程语言。那时候曾是我最喜欢的编程语言，也是最能让我感到成就感的编程语言，不过在这行学多了，还是不得不放弃易语言开发，原因就是因为易语言不够强大，但也莫得办法，如今易语言的生态就是如此。

**如果易语言不是我的第一门编程语言，那么其他编程语言就是最后一门。**
