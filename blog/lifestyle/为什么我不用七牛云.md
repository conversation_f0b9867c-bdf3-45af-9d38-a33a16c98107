---
slug: why-i-dont-use-qiniu-cloud
title: 为什么我不用七牛云
date: 2020-12-23
authors: kuizuo
tags: [随笔, cloud-service]
keywords: [随笔, cloud-service]
---

七牛云是国内鲜有的免费提供对象存储服务的一个云服务商，和腾讯云，阿里云一样，但这两者收费，而七牛云不收费，当然也不是绝对免费，对象存储免费空间 10g，每个月还有 10g 的 cdn 加速服务（多数人基本用不完），超出部分额外收费，此外 https 收费。

<!-- truncate -->

我**之前**使用七牛云的主要原因，就是业内太多人推荐了，免费还带加速，存储博客图片好的不行，然而发生了如下的事情：

## 事情经过

让我不用七牛云的罪魁祸首其实是 Chrome 浏览器，先看一张图片。

![image-20201214211848873](https://img.kuizuo.me/20201214211848873.png)

What？图片呢？第一时间毫不犹豫打开控制台查看问题所在，有这样几行报错

![image-20201214212056058](https://img.kuizuo.me/20201214212056058.png)

关注第一行

```
Mixed Content: The page at '<URL>' was loaded over HTTPS, but requested an insecure element '<URL>'. This request was automatically upgraded to HTTPS, For more information see <URL>
```

翻译过来就是：网页混合内容:页面是通过 HTTPS 加载，但却请求一个不安全元素 HTTP。该请求被自动升级为 HTTPS，更多信息请参见。

管他啥意思，先百度，然后才了解到，在 Chrome 浏览器高版本中（具体多少忘了），如果当前站点是 https，那么会自动将页面请求的 http 升级为 https，也就是说，我当前站点是 https 协议，访问不了 http 的资源，然而这可坑惨我了，我的图片全都放在七牛云上，然而七牛云的 HTTPS 是收费的，那时候我也抱着白嫖的心态，去嫖了七牛云的对象存储来做图床，现在我将http://kzcode.cn 升级为https://kzcode.cn 的时候，就意味这我不能白嫖了？也就是出现了如上画面，然后又去相关了一些百度相关的知道，看看有没有解决办法，如下

- 使用其他浏览器

这个问题只有 Chrome 浏览器内才有，在 https 站点会将 http 请求自动升级为 https，在其他浏览器不会，上面的图片也会正常显示。

- 要么都用 HTTP，要么都用 HTTPS

http 站点去请求 https 资源会不安全，而 https 站点去请求 http 会自动升级为 https，而这没有很好的有效方法去让两者兼容。

## 选择

既然搜到解决办法后，我心想，这网站怎么能不上 HTTPS，怎么能让左上角的锁不安全呢。用了 HTTPS 是不可能回去的了，同时怎么能保证别人用的浏览器不是 Chrome 浏览器呢，于是毅然决然的将所有将图片升级为 HTTPS 了，然而在对比后各家的对象存储服务，我选择了腾讯云，先放一张比对图：

| 运营商 | 价格(元/GB)                                      | 活动                                                                   |
| ------ | ------------------------------------------------ | ---------------------------------------------------------------------- |
| 阿里云 | HTTP 0.24<br/>HTTPS 需要额外加一点请求费         | 无                                                                     |
| 腾讯云 | HTTP 和 HTTPS **统一 0.21**                      | 前 6 个月每个月送 20GB 国内流量                                        |
| 七牛云 | HTTP **0.24** (超出免费 10GB)<br/>HTTPS **0.28** | 每个月送 **10GB HTTP 流量** (国内外均可使用) 以及 5 万次动态加速请求数 |

可以看到如果你每个月的请求流量都在 10GB 以内，且不用 HTTPS，七牛云肯定是最好的选择，然而我目前的站点是需要 HTTPS 的，并且七牛的 HTTPS 费用高出其他两家，甚至可以说，用了 HTTPS，就没必要选择七牛云！一没优势，速度优化不到哪里去，二是你完全可以相信大厂，三是服务费用还比两者贵。同时我云服务器也是腾讯云的，肯定优先选择腾讯，于是把对象存储换为了腾讯云，早知道就一开始就直接用腾讯的对象存储服务，果然还是花钱实在，白嫖太麻烦了。

> 参考链接 [阿里云、腾讯云、七牛云 CDN 对比](https://blog.txzhou.com/website/compare-cdn.html)

## 最后

本文的标题并不是说七牛云不好，而是我所遇到的情形让我放弃了使用七牛云，相信你看完了上面所说的，能对你的网站有个存储有个明确的配置，你在哪买服务器了，还是在哪去买其他相关的业务，没必要花费时间去折腾，说到底还是花钱实在。并且价格实际上对一个小网站来说，已经是可以非常低了（当然还是有些人会想着白嫖）

总结下来其实就是，如果你的网站不准备挂 SSL 证书，也就是通过 http 请求访问，那么白嫖七牛云，没问题，好用，但如果你的网站一旦挂了 SSL 证书，我的建议是直接删了七牛云的对象存储。
