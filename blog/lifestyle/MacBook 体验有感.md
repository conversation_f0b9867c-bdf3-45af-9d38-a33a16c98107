---
slug: /macbook-experience
title: MacBook 体验有感
date: 2023-05-05
authors: kuizuo
tags: [macOS, MacBook, 记录, 使用体验]
keywords: [macOS, MacBook, 记录, 使用体验]
description: 作者是一位从来没用过苹果产品的程序员，但在使用了一周的 MacBook Pro 14 寸后，便爱不释手。
image: https://img.kuizuo.me/202312270236590.png
---

首先我不是 iphone 用户，甚至是果黑（苹果的小黑子，合理来说是苹果手机的小黑子），所以我一向从内心就很摈弃苹果的产品。因此我从来没体验过 MacOS 系统，用了近 4 年 window，不过由于我的那台 window 本 （21 年小新 pro14） 给我的体验非常差，虽然说续航勉勉强强足够支撑我一个下午的开发，但 intel 的 i5 cpu 我就没打算将其作为主力机开发（根本做不了），更多是使用向日葵远程桌面软件来远程连接到我的台式电脑，远程操控来进行开发。然而由于屏幕分辨率不同以及网络延迟，这样的体验长期下去必然会崩溃。因此**更换自己的移动办公设备已经成了我当下的刚需。**

见识到诸多程序员大神都将 mac 作为主力开发机器，同时又被安利过很多次 MacBook，但我一直对 macOS 保持观察的态度，自己从未亲自体验过，最多也就看别人用用，在 window 上这些同样也能实现，何必要多此一举再去了解一个新的系统，新的操作逻辑。但直到我真正接触并体验过 macOS 后，我便爱不释手。

在写这篇文章时，我已经用了近一周的 macbook，因此想分享个人的购买流程、选购建议、使用感悟，或许对于某些想要尝试 MacOS 但又保有迟疑态度的用户有所帮助，也算是给曾经的我对苹果的偏见的转变。

> 拖更了近两个月的博文了，摆了一整子，说来也确实有些许惭愧。不过目前生产力工具已就绪，也要开始步入正轨。

<!-- truncate -->

## 购买流程

我是在线下 apple 授权店买的，五一假期的前一天晚上逛商场的时候正好经过，于是进去与店员交谈了一整子，又思考了一晚上，最终决定第二天直接现货 购买了丐版（标准版）的 macbook m2 pro 14 寸 16g + 512g。

至于选择 14 寸还是 16 寸，就因人（钱）而异，去线下给我最大的感受就是 16 寸是真的大，且厚重（14 寸也挺重的有 1.6kg），通常我在室内我就会拓展外接显示器加上偶尔有床上办公的需求所以在看到第一眼后便毫不犹豫的选择 14 寸作为我的目标尺寸。

至于说选择 m2 pro 还是 m2 max，这条[链接](https://www.apple.com.cn/macbook-pro-14-and-16/)与下图能告诉你答案。

![Untitled](https://img.kuizuo.me/202305050428893.png)

其次就是选配方面，在之前我是打算购买 32g + 1t 的。但在如今一堆 electron 应用（一个就要吃至少 100m 的内存），加上我本身的会有多开几个 vscode 以及多个浏览器标签， 16g 内存在 window 下对于开发从事者而言已经不够使用了，在 mac 上 从我的事实也证明 16g 内存 在两个正在运行中的 node 与 的 10 来个浏览器标签，加上一些常用软件（微信、QQ、飞书、）是有些不够用了，以下对应的活动监视图（window 中的任务管理器）

![Untitled](https://img.kuizuo.me/202305050429190.png)

虽说有 swap，表面上的 16g 物理内存实际上运行内存可能会更多，但最主要还是看内存压力。不过即使是这样，系统也没有出现过任何的卡顿，这要是换 windows，恐怕已经蓝屏了。等哪天内存压力变红时或者出现卡顿现象，再来汇报相关进程。（新买的机子，不舍得压力测试折腾她）

而 m2 pro 的 512g 相比 1t 读写速度减少一半（看下图你便懂了，单通道的速度和两个 512g 组双通道相比），事实上在之前的丐版都存在这种问题，可以说苹果是巴不得你加购硬盘容量，不然硬盘速度缩半。

![Untitled](https://img.kuizuo.me/202305050428895.png)

而 1t 及以上容量自然是无该问题，何况不加配的 mac 能叫 mac 吗。我其实是很想加配的，但无奈无现货，并且官网定制这个配置（m2 pro 32g + 1t）的还需要等待 2 个星期，据店员说“这只是预计，实际可能会更久“，不过这里不排除店员这么说诱导我在购买丐版现货。

![Untitled](https://img.kuizuo.me/202305050428896.jpeg)

不过最终能让我购买的很大原因是在五一期间我实在不想用那破 window 本，因此第二天再次联系店员决定直接付款拿丐版现货。因为考虑两年后大概率也会更换电子设备（距离我上次更换电子设备也过去两年），所以综合考虑当前的配置在这两年应该是足以使用（这句话也许说的有点早）。

实体店与线上购买没有本质的区别，**价格上都是一致的**，也是有教育优惠的。只是人家会帮你激活设备，在你的眼皮子底下看看有无问题，确认无误后，交钱走人便可。此外可能还会赠送一些额外的一些配件，如键盘膜，屏幕膜，清洁套装，拓展坞（没有手提包），不过这些对我来说都非刚需，只要 MacBook Pro 没问题即可。

不过这里想要提一下，人家是挺极力推荐我购买 apple care 的（顺带有个配件券），据我了解，貌似是有一个 apple care 的指标，需要达到多少这样。不过我个人不喜欢买保险，因此便没有购买。

## 刚到手的 Mac 该如何处理

### 不要贴膜

**不要贴膜，不要贴膜，不要贴膜!**

我本是不想贴膜，喜欢裸机的感觉，但由于附送一个屏幕膜，我便勉为其难的贴一下，然而当我贴完后我随即将辛辛苦苦贴好的膜又给撕了下来了，并不是因为我贴的不好，而是**贴膜简直就是负提升**，前后对比是肉眼可见明显，这里我用相机拍不出肉眼那种效果，如果说喝枸杞是养生，那看 macbook 屏幕说是养眼可一点不为过。毕竟维修一个 Macbook 屏幕就需要 5000 左右，让我萌生一丝购买 apple care 的想法。

其次就是网上都有流传 macbook 的 B 面(屏幕)与 C 面(键盘)之间的间隔特别薄，贴屏幕膜或键盘膜可能都会让这层素质极高的屏幕受到一些损害。还有贴屏幕膜后，在下一次更换屏幕膜的时候，可能会导致屏幕涂层脱落，而贴键盘膜的话，时间久了会导致合盖的时候键盘膜印在屏幕上。总之，基本都是建议裸机。

### 熟悉 mac 操作以及相关软件

这里我推荐自己我自己看过的几个 mac 相关指南，能够帮助小白速度上手 mac。

[Mac 云课堂的个人空间\_哔哩哔哩\_bilibili](https://space.bilibili.com/41062266)

[【看完秒懂】Mac 苹果电脑超详细上手入门指南！建议做笔记！up 良心制作，用一集视频包你熟练上手 Mac\_哔哩哔哩\_bilibili](https://www.bilibili.com/video/BV1PF411E7LG/)

下面这个会比较针对与程序开发

[2022 我用 MacBook Pro 整一年 【感想 与 踩坑指南】 - 掘金 (juejin.cn)](https://juejin.cn/post/7181274704659873850)

由于我使用时间较短，因此软件方面我不好做出评价与推荐，这里我只附上一张我已安装应用截图

![Untitled](https://img.kuizuo.me/202305050428897.png)

### 熟悉触控板与应用全屏，提升效率

如果要说使用的这段期间对笔记本电脑的体验变化无意有两点，一是颠覆我对笔记本触控板难用的想法，二是应用全屏（配合台前调度）却能够有如此高效的体验。

这一部分我认为不必过多吹捧，亲自到线下实体店感受才最为真实，相信我，你会爱上触摸板了。

### 养成保养电池的习惯

以下内容为使用 8 个月后的补充。

由于内存的不足加上重度开发，现今我的电池容量已达到 93% 了，说实话是有点小心痛的😭。

![](https://img.kuizuo.me/2024/0114155525-202401141555937.png)

这与我一些不好的使用习惯还是有关的，我通常会打开好几个标签页，好几个 应用而不关闭，这就导致内存时常处于高负载状态下，不断的从硬盘中拿内存，这样不仅伤硬盘同时也伤电池。此外我通常会用 Type-c 一线连接外接显示器（既可以充电又可以显示），加上我没有拔电以及关机的习惯(可能要过好几周才会重关一次)，这就导致 8 个月的时间内电池最大容量缩减。

现在来看或许当初买个 apple care 还能免费换个电池似乎还划得来。

## 选 windows 还是 macOS ？

现在可以毫不犹豫的说，我会选择 macOS，下一台笔电也会选择 macOS 系统。但并不是说什么场景，macOS 都是最优选，就比如说游戏需求，我想没人会买台 mac 来作为自己的游戏机。mac 上几乎玩不到什么 3A 大作，甚至在 m2 芯片上，你可能都无法下载 wegame 来玩上一把英雄联盟。

![Untitled](https://img.kuizuo.me/202305050428898.png)

如果你有桌面游戏的需求，建议拉黑 mac。此外还有一些 window 的专业软件，你在 mac 上可能找不到与之对应或平替的软件，尤其是在大学课程中，老师几乎不可能给学生发个 dmg 文件，如果你在大学期间买 mac，又要兼顾学校的课程软件需求，又不得不安装 window 虚拟机，与其如此折腾不如一开始就选用相对便宜的 win 本，还能减少一些经济压力。不过我觉得大学老师上课所说的一些软件都没必要安装，反而占用一些不必要的空间，（vc++、eclipse 等等），如果你们老师提到了 Vscode 那当我没说。

但出色的系统、高素质的屏幕，注定能让 MacBook 能够成为某部分群体的生产力工具，挣钱的机器。选用 macbook 的用户想必都希望在它任职期间产生数十倍的价值，当然排除我这个买来尝鲜的。

## 开发上的体验提升

目前手头的三台电脑设备对应的 CPU（性能从高到低）M2 Pro > AMD 5900x > i5-11300H

![Untitled](https://img.kuizuo.me/202305050428899.png)

![Untitled](https://img.kuizuo.me/202305050428900.png)

这里我没找到比较好的前端 benchmark 项目，但就从我个人直观的体验与在这三台机器启动同一个前端项目启动打包来看，在冷启动上，m2 pro 耗费 1.7s, 5900x 耗费 2.8s，i5-11300H 我都不想拉项目，去年的暑假靠这台 win 本进行开发，别提体验有多差，每次都需要干巴巴的干等项目完全启动就需要等个 2、3 分钟（不夸张），有时候可能因为某些特殊原因需要重启服务，好的，又浪费个 2、3 分钟。影响你效率的可能不只是环境，还有你的机器。

冷启动都能有近 1s 的优势，就别提热加载和打包速度上，这里直接给出我打包一个 Nuxt 项目的打包时间输出耗时，m2 pro 耗时 27s、5900x 耗时 116s（数据真实有效），快的让我有点感觉我是不是少写了某部分代码，还是说多注释了些代码。

性能优势可能不是最大的优势，但编程环境上 Mac 绝对比 Window 来的好，一个 [Homebrew](https://brew.sh/) 就已经能解决百分之 90 的编程语言环境，而这换到 Window 上则有诸多的安装方式。至少你不必像 Window 那样还需要打开设置面板配置环境变量。而 MacOS 与 Linux 又非常相似，都可以在命令行中运行 Unix、bash/zsh、以及其他 shell 命令. 所以至少从 `代码` 开发方面, Mac 绝对比 Window 来的好，这也是多数开发人员选择 Mac 的原因。

## 结语

相对遗憾的是购买的还是相对匆忙，就是没有加配 32g，虽说目前来说 16g 勉强能够应付绝大部分场景，但免不了后续爆内存，又无能为力的情况。但想到自己仿佛挖到了一个新世界的宝藏，这种担忧就显得不足为惧。

在写完这篇稿子时，回头用起 win 时，都习惯性的按下 `Alt + C` 键位，殊不知 `Ctrl + C` 才是 win 的复制。适应也许只需要几天的时间，但回去也许可能大半辈子都不再回去。

从被别人安利到用 mac，再到自己安利别人用 mac，这种对 macOS 系统相见恨晚的感受，也许只有使用过 macOS 的人才能理解。**很多东西只有自己用过才知道，只有尝试过，才知道适不适合自己。不尝试并不会丢失什么，但尝试过后往往能够收获意想不到的东西。**

如果你还没有尝试过 macOS 系统，那么你或许真的错过了很多。
