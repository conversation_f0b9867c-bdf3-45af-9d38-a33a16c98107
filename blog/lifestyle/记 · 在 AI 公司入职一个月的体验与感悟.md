---
slug: experience-of-an-ai-company
title: 记 · 在 AI 公司入职一个月的体验与感悟
date: 2024-06-10
authors: kuizuo
tags: [AI, 工作, 记录]
keywords: [AI, 工作, 记录]
description: 这篇文章分享了作者在一家 AI 公司入职一个月的心得和体会，包括工作中的挑战与成长。
image: https://img.kuizuo.me/2024/0729092038-ai-people.png
---

import Tweet from '@site/src/components/Tweet';

:::success 前文提醒

作者已于 9 月 30 日离职，以下内容只发生于入职后的 1 个月，之后就过着如同牛马般的生活，日子又开始没有盼头了😔。

::: 

已经在一家 AI 公司入职了一个月，对坐班有些厌恶的我，没想到有一天也会开始通勤打卡。而经历了这一个月的工作，我对坐班的态度有所转变，开始理解这种工作方式对我的意义。是时候分享入职这期间的工作内容与感受。

<!-- truncate -->

## 背景

直入正题，先说职位背景。该职位的技术要求大致如下，仅做参考。

```
## 任职要求 
1. 本科及以上学历，计算机科学、软件工程等相关专业, 硕士优先； 
2. 扎实的 HTML、CSS、JavaScript 基础(vanilla) 功底 ； 
3. 熟练使用 React、React Native 和 Next.js 进行前端开发 
4. 了解前端性能优化技术，如代码压缩、懒加载等 
5. 熟悉前端工程化工具 
6. 具备良好的问题解决能力和团队协作精神 
7. 熟练阅读英文技术文档 
8. 有优异前端项目开发经验者优先 

## 加分项： 
- 贡献开源社区 
- 有 AI 相关项目经验。 
- 有前端性能优化和 SEO 优化经验。 
- 有良好的产品思维和设计(UI/UX)意识。 
- 有同理心思维。
- 具有一定的审美感。
```

很贴合国外主流的技术栈（至于为何，看后文便知），比较巧的是，我的 Web 全栈学习路线就是偏国外的技术栈。因此在技术栈上，这家公司是我喜欢的，恰巧又是 AI 开发，能让我尝试到一些前沿技术，也正好是想我折腾的。

## 求职经历

我是 Boss 直聘上找的（这里没给 boss 直聘打广告，我甚至还是第一次使用 boss 直聘），我有想过找人内推，但由于家庭因素被限定在福州这座城市，而内推的所在的城市往往都是那些一线城市，加上我的八股文和算法很不过关（我也很不情愿刷），到时候面试那关估计也不乐观。

因此就在 Boss 上碰碰运气，也顺带体验一下新人都是怎么找工作的。

从五一的时候开始准备简历和项目，在5号开始投简历，投递简历一关我是直接怼着工作经验1-3年的来投，而不是投应届或实习岗。因为我确实有一些工作经验，只不过不是正常的坐班打卡的形式，这在之前的博客中有说到。

在这期间共投了20多家，基本都是已读不回，就更别说投递简历了。后来我才了解到，原来 HR 回复消息是要花钱的，发布一个岗位也是。

唯一回复的还是我现在入职的这家，而且我还投了两份过去，一份是给 HR 的（没回），一份是给技术 leader 的（leader 回了）。

![Untitled](https://img.kuizuo.me/2024/0611021351-Untitled.png)

### 面试被鸽

可能是由于当时这个岗位急招的原因，在 boss 直聘上也没多说什么，leader 就约明早 11 点来公司现场初步面试聊天一下。这期间还发生了一个小变故，我到公司了，可联系不上面试官，打了微信电话也无果。待了10来分钟后我就走了，等了约一个小时都没信息，那我大概率是被鸽了，还不提前和我通知一声，然后在boss上留下了这句评价🥲。

![Untitled](https://img.kuizuo.me/2024/0611021351-1a520a5a-c3b9-4049-bfcf-825113aa7b2c.png)

初入职场，初次面试就这种情况，说真的我当时都有点心灰意冷了，我猜想是不是因为有其他合适的人选，于是就不招我了，就连信息也不给我打一个招呼，相当于把我拉黑似得。随后我就到附近的麦当劳花了 10 元的套餐安慰了一下自己，麦！

### 开始面试

直到到下午一点多的时候，面试官回复我说当时他们在开会，期间不让携带电子设备。早上就当一面过了，问我下午有没有时间，直接二面技术面(code test)过了就直接拿offer。

这时我才知道，原来早上也仅仅只是我的猜想，但我还是有点不想去了，心情有点不太愉悦，但想了想也懒得计较了，过去就当聊天罢了。到了下午面试问的就偏前端基础、八股文那些问题，其实我回答的巨烂，确实也没好好刷题，也不喜欢刷题，就面试了。自己写代码是由业务环境下驱动的，并从中寻求最佳实践。但好在我的技术面是比较广的，很多前沿的前端相关的工具库或多或少都使用过，也能侃侃而谈，加上个人 blog 和 github 这两个大加分项。就进入到了一个代码考核测试，不限框架，不限规则，使用公司的电脑打开 codesandbox 写一个todo list，前提是不使用任何 AI 工具。

这不正好到了我的强项，之前学某个框架的时候，不知道写什么demo，就写 todo list 来练手😂。恰好这次我就使用 next.js app router + Tailwindcss 的模版并且使用 form 标签的 action 和 use server 来实现新增功能。 能体现出我有在使用 next.js，而且用上了一些新特性，就拿到 offer 了。

![1000047893.jpg](https://img.kuizuo.me/2024/0611021351-03100cc0-2b0a-4049-82ed-58cc46ac1717.png)

听完之后是不是莫名的感觉这个 offer 拿的好像有点莫名其妙的感觉😂，不管怎么样结果是好的就行了。

不过拿到 offer 后，我并没有选择马上入职，经历了一次被鸽的经历，对该公司的印象带有一些怀疑。其次就是这是一家初创 AI 公司，规模不大，从应届生找工作的角度，第一份正式的工作的起点很关键，如果能直接进大厂，后续跳槽到其他公司大概率也不成问题。

但在当地我投递了 20 多家已读不回的情况下，加上这份已有的 offer 不等人（急招），加上我家里人给我推荐的工作内容我并不是很满意，于是思考了两天，最终还是选择入职了这家公司。

### 薪资

比较令我差异的是我与企业签订的直接劳动合同，可能是因为我直接投递 1-3 年的工作经验，但我此时的身份还是应届生，按理来说我应该是签订实习合同后，转正再签劳动合同，~~难道说我已经提前转正了？~~。不过也好，这样和学校的三方协议都可以不用签了，直接给劳动合同便可。

试用期 3 个月，薪资打 8 折。薪资在我当地还算 ok，但对于我而言并不理想。可能是会的比较多(~~全栈~~？全干！)，加上曾经赚过比这还高上许多的薪资，从内心的角度多少是有些不平衡。不过目前还是试用期，薪资这方面后续也能再谈。

接下来尤为重要的上班体验才是让我觉得没后悔入职这家公司。

## 上班体验

介绍一下公司部门的办公工具

办公管理：企业微信

团队协作：Slack

任务看板：Trello 

代码仓库：Github

代码托管：Vercel

视频会议：Zoom

你会发现除了企业微信，其他的应用都是国外的。怎么看都不是一家国内的企业吧，这是因为我部门的 Leader 是海外留学的，这也就不难理解工具是国外应用，技术栈选型是 React 生态了。

入职的第一周部门开了个小会，就是简单介绍了一下部门的任务职责，每个成员自我介绍。重点是提供一个优质的学习环境，像是技术书籍，电子设备，UI 模版或是技术会议的门票等费用，只要对部门有利，能提升自己，都可以找他报销。

我已经找 Leader 报销了个 magic ui pro，大约 420 块，直接找财务刷卡，付款的感觉是真爽，我是真爱了🥰。

![Untitled](https://img.kuizuo.me/2024/0611021351-Untitled%201.png)

几天后，公司来了一个阿里做 B 端低代码开发的同事，也是负责前端开发，这不，我可以间接和这个老哥那学习大厂相关经验，我还正愁着没大厂相关的经验😄。

我询问他来这家公司的原因，他说被裁了，在家接外包一年了，不稳定就准备找工作，恰好这家公司急招，于是就来了。

:::warning 补

端午节后，这位老哥提离职了，原因的话我就不具体说了，可能是因为年龄大了，不适合坐班了。虽然早有预感，但还是有点不舍。因为现在部门的前端重任都在我这了😭

:::

### 团建

在我入职的第一周周末 Leader 为整个部门安排团建，由于这个部门成立不到 2 周，来的都是新成员，让我们自己组个局，去外面吃个饭。

也是在团建的时候了解到同事的履历一个个都不简单，有 985 的，有海外留学的，有在阿里、网易待过的，还有我这不堪回首的经历 🤡。

后面原定在 61 安排整个公司的团建，但由于天气和周末时间去的人少的因素而取消了，这我就不多说了。

端午之后的第一个工作日的中午，补过端午节部门聚餐的，这我也不多说了。就是怎么感觉这频率有点不太对，然后实际项目产出也还停留在 Spring 1 的阶段，让我有些不自在。

### 福利

部门每个月都会定一个最佳员工奖，我很荣幸获得部门本月的最佳员工，也感谢部门成员的认可，奖励是 300 元奖金或一日自由假。

![Untitled](https://img.kuizuo.me/2024/0611021351-Untitled%202.png)

甚至还有一张奖状，就是这奖状怎么有点像给小学生似的。（事后我才了解到这奖状还是用打印机打印的😂）

![Untitled](https://img.kuizuo.me/2024/0611021351-0da32f2a-c077-42c9-8a57-fff9f259187e.png)

目前我已经能感受到最大的福利就是那个 magicui 动效库的模版，当然了，这个是要给公司的官网用上的，我也是蹭公司的福，给自己的站点用上了这个动效库。

此外像节日福利，如这次端午节，就是聚餐和发粽子，这也就没什么好说的。

### 通勤

公司距离我租房的地方只有 2 公里，每日的通勤总时间大约 40 分钟，早上大约 8 点起床，我通常坐公交车到公司附近的早餐店吃个早饭，吃完差不多 8 点 40分~50 分。中午外卖就不说了。下午下班从公交车和走路做个选择，吃完饭回到家。

### 黑客松

黑客松(hackathon)，也称编程马拉松比赛。我是第一次听说过这个词，Leader 给定两个选题一个是打造某市地铁智能出行，另一个是给某商场的提供贴心的购物体验，发挥自己近一个月所学的知识，去创造一个供用户使用的 AI 程序，月底交付，奖金 3000 元/小组，抽签分组。我们当时部门有个人提了一嘴，要不我们两小组自己商量一下，把奖金平分得了😂。

不过对于这个行为，我个人认为目的是为了激励员工之间协同合作，但同时也免不了技术上的内耗，毕竟这个比赛不是我们的主要工作内容。

## 工作内容

我想肯定有很多人对 AI 开发的刻板印象是要会大模型开发，会懂得微调，会懂得人工智能算法。这个想法也没错，但从开发 AI 应用的角度，其实蛮需要前端的，尤其是会全栈框架的前端。

这里我不得不惊叹 next.js 的生态，很多 AI 相关的例子可以直接从 Vercel 的 [AI Template](https://vercel.com/templates?utm_source=next-site&utm_medium=navbar&utm_campaign=next_site_nav_templates&framework=next.js&type=ai) 下学习，预览是否有你需要的功能，Clone 到本地，然后运行项目，对某些部分进行更改。搭建 AI 应用也是异常的快。

### 仿 [AI SDK](https://sdk.vercel.ai/prompt) 网站效果

Leader 下发的一个任务，入职的前两周主要让我熟悉一些怎么使用 next.js 配合 vercel 的 ai sdk 来开发 AI 应用，如怎么调用 openai 的模型，实现一个 ai chatbot。给定了一个任务就是仿造 [AI SDK](https://sdk.vercel.ai/prompt)，由于该项目没有开源，自然就只能另辟蹊径。

首先就是仿造页面了，这个作为前端开发，实现起来也算容易，更何况这个这个页面的样式使用 Tailwindcss 编写，直接通过审查元素仿造就行了。

其次在功能实现上，ai sdk 文档都提供了非常完善的解决方案，照着文档将代码稍微改写一下便可，具体的细节就不演示了。

### 官网首页

两周后开始正式项目开发了，首当其冲的就是官网页。

这里当时 Leader 问我有没有用过 Gatsbyjs，要用这个框架搭建一个官网。我表明我没用过，但我提了一嘴如果要搭建偏内容向的网站，可以考虑 Astro，我愿意折腾一番（我也一直想学 Astro 的）。不过最终在开发时间和成本的商讨下还是选择使用 next.js 来搭建，leader 还顺带给我推荐了一个动效库 magicui，叫我看看里面的案例，看看能不能给官网加点动效。 之后就有了上文提到报销 magicui 的事。

### Rag bot

篇幅实在有限，有关 [RAG](https://aws.amazon.com/cn/what-is/retrieval-augmented-generation/) 的不做过多解释，它可以让你的 AI 应用更具有权威性，让数据的来源可靠，而非胡乱生成数据。

RAG 的基本流程就是：

1. 用户输入提问
2. 检索：根据用户提问对 向量数据库 进行相似性检测，查找与回答用户问题最相关的内容
3. 增强：根据检索的结果，生成 prompt。 一般都会涉及 “仅依赖下述信息源来回答问题” 这种限制 llm 参考信息源的语句，来减少幻想，让回答更加聚焦
4. 生成：将增强后的 prompt 传递给 llm，返回数据给用户

在这个应用开发中，借鉴了 [ragbot-starter](https://github.com/datastax/ragbot-starter) 这个开源项目，同时向量数据库选用 datastax 公司的Astra DB。

恰好在开发这个应用的期间，我也正好在学习 Langchain.js，所以在数据处理这部分有点得心应手，目前应用还只停留在处理本地文件或用户上传的文件，只需要配置各种 [File Loader](https://js.langchain.com/v0.2/docs/integrations/document_loaders/file_loaders/) 便可。

### 使用 RN 实现 chatbot

先看 Gif 效果。

![demo](https://img.kuizuo.me/2024/0612203208-demo.gif)

第一次用 Screen Studio，显示的不是很好，还请见谅，主要就是实现一个流式文本效果。

这里简单说下怎么实现的，就用  [react-native-reusables](https://github.com/mrzachnugent/react-native-reusables) 的模版(React native 的 Shadcn/ui) + [react-native-gen-ui](https://github.com/zerodays/react-native-gen-ui) 实现的，不过后者的功能比较单一，后续估计要改代码了。代码就不贴了，我怕涉嫌代码泄露（其实已经泄露差不多了）。


## 收获

要我说最大的收获不是遇到一个氛围不错的公司，遇到一个好 leader，也不是接触 AI 开发从中学到了什么，更不是增进了我的技术栈。而是让我养成良好的习惯，开始正常一日三餐，开始作息规律，开始将工作与生活分离，身体状态也渐渐好了起来。

下图为 5 月的生物作息，基本都保持 0 点前入睡。（不过在我写这篇文章的时候已经两点了🥱）

![Untitled](https://img.kuizuo.me/2024/0611021351-Untitled%203.png)

过去几年内我的作息与饮食都非常糟糕，能明显的感觉到状态有所下滑，编写代码的效率和能力也明显不如以前，有些力不从心。今年都快过去一半了，而我仅仅完成了2篇博文的写作，文章的输出效率明显不行😮‍💨。

在半年前我对自己当时的[现状](/blog/2023-year-end-summary#现状)做了个分析，幻想着坐班或许能改变我当下的现状。如今经历了这一个月的坐班生活，可能是因为坐班而改变，也可能是公司的氛围，不管是那种，让我跳出我原有舒适区，重新拾起对新颖事物的兴趣，重新点燃学习某个技术的热情，重新找回了自我。

## 结尾

在我还没找工作之前，从我几个同届毕业的同学和网友的反馈得知今年的就业环境异常险峻。不仅如此，我还在网络上看到了大量工作者对自身工作的抱怨与不满，这些现象让我在工作前让我对未来的就业前景感到了一些不安。

当我亲身入局感受一番，也不禁开始低声叹气。开始思考是什么原因导致了如今大环境不好的现象，人为的制造就业焦虑，还是当下现实本就如此。当我跳出思考，回到现实难道环境好就一定挣钱多，环境差就一定挣钱少吗？社会似乎并不是这么简单的等式。

我逐渐意识到，无论大环境如何，每个人的努力和选择仍是至关重要。在面对不确定性和挑战时，保持学习和进步的态度，以及寻找自己的核心竞争力，才是应对困境的关键。

真正的职业安全感并不完全来自于外部环境，而是来自于我们自身不断提升的能力和适应变化的灵活性。
