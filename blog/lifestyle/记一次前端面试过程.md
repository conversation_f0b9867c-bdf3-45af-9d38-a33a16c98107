---
slug: frontend-interview-experience
title: 记一次前端面试过程
date: 2022-06-25
authors: kuizuo
tags: [记录, 面试]
keywords: [记录, 面试]
description: 记录一次前端面试过程
draft: true
---

考试终于结束了，也将近一个月没怎么写代码和文章了，准备调整下自身状态，开始进一步的学习。然后在前段时间我的一个同校同学拿到了 xx 公司的 offer，然后该公司正好有招前端开发实习，就问我有没有兴趣尝试一下，但一开始我内心其实不是很想工作的，想着暑假去闭关学其他技术，同时加上我目前的身份**准大三(休学一年)**实习期可能也就 2 个月，毕竟这行业不同于打工，是需要长期工作和维护。不过一想毕竟没面试过，所以就蛮去尝试一下，至于最终的结果，阅读全文吧。

<!-- truncate -->

## 简历

我的那位同学给我推了面试官的微信，于是加完微信，简单的几句招呼便直接开始找我要了一份**简历**，也就是整个面试过程中最重要的物件，即**面试的前提**。

很显然我压根就没有准备过这玩意，然后面试官叫我去准备一下（期间聊天强调过几次有空准备下简历），于是就去网上搜寻了关于简历说明、编写建议与简历模板，也了解到简历对面试者的一个重要性。

**简历是面试官了解面试者最快方式**，甚至可以说对于大部分人群是非要不可。于是我花费了一天的时间去准备了一份属于自己的简历，主要内容为个人的基本信息、技术特长、个人描述、教育背景、工作经验、项目经验等，其内容一定要真实，同时也要在精简的同时，让面试官一眼就能看到你的亮点，以及将来你到贵公司工作能为其提供的帮助，这些在外面的大部分简历编写建议中也有提及，这里我也仅是简单总结。

关于我的简历就不展示了（毕竟暂时比较烂），不过关于简历模板的话，我倒是可以推荐下。我所使用的是 [木及简历](https://www.mujicv.com/)，此外还有 [Markdown 简历排版工具 (mdnice.com)](https://resume.mdnice.com/) 与 [简历自动生成 (sugarat.top)](https://resume.sugarat.top/) ，这些都是我在搜寻资料中认为不错的在线简历模板。

最主要是丰富自身的阅历与技术，这样在简历编写时，其内容是一定能吸引到面试官的注意。要是什么技能都不会，什么项目都没有，一份不起眼的简历，作为面试官是很难给你一个面试的机会。

如果你已经看到这里了，并且你未来有找工作的需求，那么现在就可以着笔简历，而不是等到要找工作的时候在准备。同时在日常开发与学习中，也可以不断去完善自身简历。**总之，越早写简历越好**。

将简历提交给面试官后，如果叫你准备下面试，那么恭喜，已经进入第二步------**面试**

## 面试

这次预定为周六早上 10 点（即本文发布日期）**线上面试**（无屏幕共享，只单纯聊聊技术栈相关），正常来说知道自己要被面的话，应该来说会提前做些准备。然而我就不一样了，我**没刷过任何面试题**（八股文），所以我想看看我的知识储备能否回答出这些问题，结果也确实如此，这次面试大多数问题回答不出来或者回答的半知半解的。

### 问技术

关于所问的问题，和外面主流的前端面试大致，这些就是考验基本功的问题，例如 HTML5 的新特性，JS 的一些语法等等，这些在八股文在面试过程中是必须要背的，因为这些决定计算机的基本功。此外面试官在了解你的大致技术栈后，必然会针对你简历上的内容，提出一些针对性的问题。比方说我在简历上写了一个后台管理系统 [KzAdmin](https://admin.kuizuo.me) 其中就有问到像 JWT，axios 封装，前端菜单展示等问题，然后和一个[JavaScript 的混淆与还原](https://deobfuscator.kuizuo.me/)，就问这个主要功能，以及其中所涉及的一些技术栈相关的。

至于整个面试所关于技术的问题与细节就不一一列举了，不过令我意外的是，所问题的竟然没有算法题，不过事后想想也对线上面试还无共享屏幕的前提下，咋可能问算法题呢，最多也就是问问你对你的技术栈的了解程度与理解情况，整个过程与我预期所想符合。

### 闲谈

最后就是表明他们的项目可能是需要长期的，然后我目前的情况又相对比较特殊，问我下学期课程的安排啥的。相对面试者比较在意的就是薪资，但在这次谈论期间是没有关于薪资的，我和面试官都没提，我自然是不敢提的，毕竟问题都回答的那么烂了，哪里还好意思提，同时这是实习岗位，自然薪资不会高到哪里去的。

当然了，整个面试过程最后一句话是**回去等通知**

### 等待面试结果

通常面试都有以下三种结果。

第一、现场录用。第二、当场拒用。第三、回去等通知

大多数面试结果都是第三条，我自然不例外。至于有没有二面啥的，自我感觉有个 7 成左右。为何不敢说百分百呢？从整个面试的过程来看，其实我准备时间是充分的，但根本没怎么准备，甚至可以说全程是种摆烂的态度。再从回答问题上来看，我整个回答情况就表明告诉面试官我没准备好这次面试，加上一开始没准备好简历，就相当于我就是来面着玩的 😂。

公司面对这类面试者（求职意向不是很明确，情况特殊），大概率就是直接刷掉了。当然，这只是我的个人对此次面试的看法，如果有二面的话，我一定好好刷面试题，顺带锻炼下表达能力（现在在回想整个回答过程，我甚至都不好意思说我是学前端的了）。如果没有的话，算了不去想了。

## 面试结果

承接上文，直到考试结束任然没收到任何通知，即挂了。其原因其实我也在上文中说到，我的面试态度相对比较差，加上实习期限的原因。不过后话来说，即便我收到了这份 offer 我也不一定会去，当然这里说这种话确实有那么一点不好，但首先我是工作过（虽然不算一个体系的公司，只能算作工作室），而且给我分配的时间充裕，而且薪资还不低，并且当时涉及的业务还是我相对比较强项的协议复现和逆向（虽然我已经快半年没碰，而且大概率会在持续半年的时间不接触逆向相关的）。但每天的任务就是等待产品经理的需求，这时就需要停下手头的事情，来完成任务，大部分的时间都是在测试和维护。虽说和与搬砖打工有点区别，但本质无异，都是枯燥任务。同时我本身是挺反感重复任务与中断的，所以上一份的工作给我的感受就是（时间）充实但又夹杂着（任务）枯燥，但很多时候就不得不妥协，因为生活所迫，这里就不做感慨了。

总之，这次的面试结果也并不让我意外，甚至可以说是情理之中。

## 总结与建议

对整个面试的过程对我而言就是要锻炼自身表达能力，在除了没刷八股文外，像一些问题，我很难用口语去告诉面试官我的意思，但使用屏幕共享加代码展示就不一样，但当时的面试官没提加上我也没主动申请，这是我认为整个过程比较可惜的一点。不过整个过程下来，我认为面试也没什么的，无非就是问问题，平常多刷面试题。

关于一些建议的话：

首先就是个人简历非常重要，最好添写个人[Github](https://github.com/)与[博客](https://kuizuo.me)，绝对是面试的加分项。尤其是博客，在上面所分享的内容，也可能会吸引到一些需要招聘你的 HR。至少告诉面试官你有在学习，并且懂得记录。其次就是简历上一定要有能亮眼的地方同时自己又很掌握的项目经验，这样面试官针对性的问题，对自身也有很好的作答。如果自己掌握程度不是很大的情况下，还是不建议编写，即便学过，但忘了，那就是不会（说的不就是我吗）。

一个好的简历谁都会写，但面试不是一纸千金，工作更不是纸上谈兵。简历是了解一个人的基本资料，而面试主要是考察一个人的工作能力与综合素质，即便你工作能力再强大，但你工作态度属于摸鱼的状态，同样是无法胜任此工作的。所以既然选择要工作，那就应该做足准备，拿出最好的工作态度给面试官。

所以我一开始的目的到底是什么呢？准备实习工作呢？还是闭关学习呢？又或者是...
