---
slug: learning-style
title: 浅谈个人学习方式
date: 2022-06-10
authors: kuizuo
tags: [杂谈]
keywords: [杂谈]
---

临近考试周，又要开始准备复习，顺带总结下自己平常的学习方式与一些感慨。

<!-- truncate -->

我个人主要的两种学习方式，**主动学习**与**被动学习**

## 主动学习 ✍

通常来说，主动学习往往是一件痛苦的事情。就如考试周，平常的课该去在课堂上睡觉，该不去的在宿舍睡觉，到考试周，一学期的课没听，但一想到不复习就有可能面临挂科，或多或少都会复习。考试摆烂，挂科惨淡。

除了上面是会导致坏结果而去学习，那么还有一种则是与之相反，也就是所需，所感兴趣的去学习。

例如我想要实现一个酷炫功能，或者是一个需求时，而其中需要的知识点便会去了解与学习。

对于前者而言，其过程并不舒服，甚至可以说是逼自己学习不喜欢的东西，通常我是不如不学的，即便学了在以后也难以记起，除非不学的结果比较严重（如挂科）。而后者情况便不同，兴趣是最好的老师，甚至都不需要借助外界因素去激励，便能有一个很好的学习成果。

如果没有兴趣或需求的情况下，那么主动学习一定要定制一系列的目标，如果没有明确的目标，学习将会显得十分迷茫。我有很多时候便是这样，想去主动去学习诸多技术栈，不知从何下手。

## 被动学习 📘

其实可以说是利用业余时间学习的一个方式，首先我会想我一天主要做的事情（通常是与学习无关），比如刷各个平台（b 站，抖音）的视频，刷知乎，qq 群闲聊等等，这里只是以我一天的大部分日常举例。

此事不妨关注一些与学习类的账号，包括但不限于加入一些技术交流群， up 主，公众号，博主等等，当你每次刷这类平台时，系统很自然就会给你推送相关内容，比方说 b 站，我日常会在上面刷一些鬼畜视频，生活娱乐视频，但也会关注一些技术类的账号，在娱乐的同时，还能不经意间刷到一些编程知识（知乎，微信公众号同理），这送上门的知识不香吗？

再比如 watch 一些开源项目，订阅一些博客文章，定时推送 Hacker News 周刊等等，在他们发布一些重要内容的时候能及时通知到你。虽然这样每天邮箱时常处于未读状态，但如果打开某一条邮箱查看其中的内容，说不定又是一次不错的收获。

当然，这样学习肯定有一定弊端。首先所摄取的知识过于笼统，一般都是由别人推荐的，甚至有可能会浪费你人生中的几分钟，因为这些知识点可能你已经掌握了，或者这些知识确实没什么干货。其次所获取到的知识往往是整个知识面的冰上一角，或者是某些新型技术，但想要深入去学习还是得按上面的主动学习。

被动学习主要的作用能让你在不学习的状态下，还能获取相对应的知识。听起来可能觉得很卷，无时无刻的在学习，但我认为在娱乐中学习往往不会显得像主动学习那般有种疲惫感。

我在被动学习中就间接了解到许多的前沿的技术栈，以及了解到许多之前没使用过的语言、框架特性，而这些都是直接送上门的知识点，而我要做的只是关注一些账号，订阅一些文章或站点，而不用自己去茫茫知识海洋中去寻求。

## 两者的权衡

我时常处于这类被动学习状态，因为我对很多技术点都很感兴趣，但又不知道从何下手，而当我刷到我所感兴趣的东西时，我才会开始转为主动学习。之所以会出现这样的学习状态转型，有很大一部分是因为**兴趣感已经没有一开始所学的那么强烈，甚至可以说有些许乏味**。

在一开始接触时，我是抱着强烈的兴趣感去学的，处于一种非常积极的主动学习状态，当时所获取的知识是无法用现在同等时间所堪比。然而在兴趣感消散，这种状态都为浮云。主动学习的频率减弱，每天都在做着与学习无关的事情，这期间与知识没有任何的往来。

直到将每天所要做的事情，间接的转为被动学习的方式，至少有在学习，而不是在停步，即便是行走 1 厘米，那也是米。

## 总结

最好是对感兴趣的方面学习，这样即有动力去学习，有不会感到厌烦，并且兴趣是能陪伴很久的，也往往能坚持下去。

无论是任何事情在任何时候下都应该保持的是一种不断坚持的状态，打游戏也好，学习也好。

一段时间不打游戏，手感生疏，再次接触就没状态。一段时间不学知识，容易忘记，再次使用就没印象。

大部分人都是如此，可谁又不希望保持一种不断坚持的状态。毕竟不是每个人都有充裕的个人资源与时间资源，很多时候这些资源并不是由自己分配的，有可能是亲朋好友，也有可能是上司老板，最有可能是生活所迫。。。

**唯愿此生，岁月静好**
