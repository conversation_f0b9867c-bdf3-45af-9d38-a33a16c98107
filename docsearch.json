{"index_name": "kuizuo", "start_urls": ["https://kuizuo.me/"], "sitemap_urls": ["https://kuizuo.me/sitemap.xml"], "selectors": {"lvl0": {"selector": "(//ul[contains(@class,'menu__list')]//a[contains(@class, 'menu__link menu__link--sublist menu__link--active')]/text() | //nav[contains(@class, 'navbar')]//a[contains(@class, 'navbar__link--active')]/text())[last()]", "type": "xpath", "global": true, "default_value": "Documentation"}, "lvl1": "header h1, article h1", "lvl2": "article h2", "lvl3": "article h3", "lvl4": "article h4", "lvl5": "article h5, article td:first-child", "lvl6": "article h6", "text": "article p, article li, article td:last-child"}, "custom_settings": {"attributesForFaceting": ["type", "lang", "language", "version", "docusaurus_tag"], "attributesToRetrieve": ["hierarchy", "content", "anchor", "url", "url_without_anchor", "type"], "attributesToHighlight": ["hierarchy", "content"], "attributesToSnippet": ["content:10"], "camelCaseAttributes": ["hierarchy", "content"], "searchableAttributes": ["unordered(hierarchy.lvl0)", "unordered(hierarchy.lvl1)", "unordered(hierarchy.lvl2)", "unordered(hierarchy.lvl3)", "unordered(hierarchy.lvl4)", "unordered(hierarchy.lvl5)", "unordered(hierarchy.lvl6)", "content"], "distinct": true, "attributeForDistinct": "url", "customRanking": ["desc(weight.pageRank)", "desc(weight.level)", "asc(weight.position)"], "ranking": ["words", "filters", "typo", "attribute", "proximity", "exact", "custom"], "highlightPreTag": "<span class='algolia-docsearch-suggestion--highlight'>", "highlightPostTag": "</span>", "minWordSizefor1Typo": 3, "minWordSizefor2Typos": 7, "allowTyposOnNumericTokens": false, "minProximity": 1, "ignorePlurals": true, "advancedSyntax": true, "attributeCriteriaComputedByMinProximity": true, "removeWordsIfNoResults": "allOptional", "separatorsToIndex": "_", "synonyms": [["js", "javascript"], ["ts", "typescript"]]}}