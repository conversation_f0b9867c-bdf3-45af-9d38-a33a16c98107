{"playground.codesandbox.description": {"message": "CodeSandbox is a popular playground solution. Runs Docusaurus in a remote Docker container."}, "playground.stackblitz.description": {"message": "StackBlitz uses a novel {webContainersLink} technology to run Docusaurus directly in your browser."}, "playground.tryItButton": {"message": "Try it now!"}, "Hello! 我是": {"message": "Hello! 我是", "description": "hero greet"}, "愧怍": {"message": "愧怍", "description": "my name"}, "homepage.hero.text": {"message": "在这里我会分享各类技术栈所遇到问题与解决方案，带你了解最新的技术栈以及实际开发中如何应用，并希望我的开发经历对你有所启发。", "description": "hero text"}, "homepage.hero.look": {"message": "你可以随处逛逛，查看{note}、{project}、{link}、以及我的{idea}。", "description": "hero look"}, "hompage.hero.note": {"message": "技术笔记", "description": "Note link label"}, "hompage.hero.project": {"message": "实战项目", "description": "Project link label"}, "hompage.hero.link": {"message": "网址导航", "description": "Link link label"}, "hompage.hero.idea": {"message": "想法感悟", "description": "Idea label"}, "homepage.qqgroup": {"message": "QQ 群：5478458", "description": "qq group"}, "自我介绍": {"message": "自我介绍", "description": "follow me btn text"}, "阅读全文": {"message": "阅读全文", "description": "read full text"}, "theme.tags.tagsPageTitle": {"message": "标签", "description": "The title of the tag list page"}, "blogtagpage.title": {"message": "下的博客", "description": "blog tag page title"}, "blogtagpage.title.alt": {"message": "", "description": "blog tag page title in alternate order"}, "blogtagpage.description": {"message": "博客标签", "description": "blog tag page description"}, "blogtagpage.count.label": {"message": "篇", "description": "blog page count label"}, "blogtagpage.seeall.label": {"message": "查看所有标签（分类）", "description": "blog page see all label"}, "theme.ErrorPageContent.title": {"message": "页面已崩溃。", "description": "The title of the fallback page when the page crashed"}, "theme.ErrorPageContent.tryAgain": {"message": "重试", "description": "The label of the button to try again when the page crashed"}, "theme.NotFound.title": {"message": "哎呀，页面失踪了~", "description": "The title of the 404 page"}, "theme.NotFound.p1": {"message": "找不到你要访问的页面，要不点点别处看看？", "description": "The first paragraph of the 404 page"}, "theme.NotFound.p2": {"message": "可能是个 Bug🐛，也可能是作者偷懒还没写😴", "description": "The 2nd paragraph of the 404 page"}, "theme.AnnouncementBar.closeButtonAriaLabel": {"message": "关闭", "description": "The ARIA label for close button of announcement bar"}, "theme.blog.archive.title": {"message": "历史博文", "description": "The page & hero title of the blog archive page"}, "theme.blog.archive.description": {"message": "历史博文", "description": "The page & hero description of the blog archive page"}, "theme.BackToTopButton.buttonAriaLabel": {"message": "回到顶部", "description": "The ARIA label for the back to top button"}, "theme.blog.paginator.navAriaLabel": {"message": "博文列表分页导航", "description": "The ARIA label for the blog pagination"}, "theme.blog.paginator.newerEntries": {"message": "较新的博文", "description": "The label used to navigate to the newer blog posts page (previous page)"}, "theme.blog.paginator.olderEntries": {"message": "较旧的博文", "description": "The label used to navigate to the older blog posts page (next page)"}, "theme.blog.post.readingTime.plurals": {"message": "{readingTime} 分钟阅读", "description": "Pluralized label for \"{readingTime} min read\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.blog.post.readMoreLabel": {"message": "阅读 {title} 的全文", "description": "The ARIA label for the link to full blog posts from excerpts"}, "theme.blog.post.readMore": {"message": "阅读全文", "description": "The label used in blog post item excerpts to link to full blog posts"}, "theme.blog.post.paginator.navAriaLabel": {"message": "博文分页导航", "description": "The ARIA label for the blog posts pagination"}, "theme.blog.post.paginator.newerPost": {"message": "较新一篇", "description": "The blog post button label to navigate to the newer/previous post"}, "theme.blog.post.paginator.olderPost": {"message": "较旧一篇", "description": "The blog post button label to navigate to the older/next post"}, "theme.blog.sidebar.navAriaLabel": {"message": "最近博文导航", "description": "The ARIA label for recent posts in the blog sidebar"}, "theme.blog.post.plurals": {"message": "{count} 篇博文", "description": "Pluralized label for \"{count} posts\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.blog.title.recommend": {"message": "推荐阅读"}, "theme.blog.title.new": {"message": "最新博客"}, "theme.blog.tagTitle": {"message": "{nPosts} 含有标签「{tagName}」", "description": "The title of the page for a blog tag"}, "theme.tags.tagsPageLink": {"message": "查看所有标签", "description": "The label of the link targeting the tag list page"}, "theme.colorToggle.ariaLabel": {"message": "切换浅色/暗黑模式（当前为{mode}）", "description": "The ARIA label for the navbar color mode toggle"}, "theme.colorToggle.ariaLabel.mode.dark": {"message": "暗黑模式", "description": "The name for the dark color mode"}, "theme.colorToggle.ariaLabel.mode.light": {"message": "浅色模式", "description": "The name for the light color mode"}, "theme.docs.DocCard.categoryDescription": {"message": "{count} 个项目", "description": "The default description for a category card in the generated index about how many items this category includes"}, "theme.docs.sidebar.expandButtonTitle": {"message": "展开侧边栏", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.sidebar.expandButtonAriaLabel": {"message": "展开侧边栏", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.paginator.navAriaLabel": {"message": "文档分页导航", "description": "The ARIA label for the docs pagination"}, "theme.docs.paginator.previous": {"message": "上一页", "description": "The label used to navigate to the previous doc"}, "theme.docs.paginator.next": {"message": "下一页", "description": "The label used to navigate to the next doc"}, "theme.DocSidebarItem.toggleCollapsedCategoryAriaLabel": {"message": "打开/收起侧边栏菜单「{label}」", "description": "The ARIA label to toggle the collapsible sidebar category"}, "theme.docs.tagDocListPageTitle.nDocsTagged": {"message": "{count} 篇文档带有标签", "description": "Pluralized label for \"{count} docs tagged\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.tagDocListPageTitle": {"message": "{nDocsTagged}「{tagName}」", "description": "The title of the page for a docs tag"}, "theme.docs.versionBadge.label": {"message": "版本：{versionLabel}"}, "theme.docs.versions.unreleasedVersionLabel": {"message": "此为 {siteTitle} {versionLabel} 版尚未发行的文档。", "description": "The label used to tell the user that he's browsing an unreleased doc version"}, "theme.docs.versions.unmaintainedVersionLabel": {"message": "此为 {siteTitle} {versionLabel} 版的文档，现已不再积极维护。", "description": "The label used to tell the user that he's browsing an unmaintained doc version"}, "theme.docs.versions.latestVersionSuggestionLabel": {"message": "最新的文档请参阅 {latestVersionLink} ({versionLabel})。", "description": "The label used to tell the user to check the latest version"}, "theme.docs.versions.latestVersionLinkLabel": {"message": "最新版本", "description": "The label used for the latest version suggestion link label"}, "theme.common.editThisPage": {"message": "编辑此页", "description": "The link label to edit the current page"}, "theme.common.headingLinkTitle": {"message": "标题的直接链接", "description": "Title for link to heading"}, "theme.lastUpdated.atDate": {"message": "于 {date} ", "description": "The words used to describe on which date a page has been last updated"}, "theme.lastUpdated.byUser": {"message": "由 {user} ", "description": "The words used to describe by who the page has been last updated"}, "theme.lastUpdated.lastUpdatedAtBy": {"message": "最后{byUser}{atDate}更新", "description": "The sentence used to display when a page has been last updated, and by who"}, "theme.navbar.mobileVersionsDropdown.label": {"message": "选择版本", "description": "The label for the navbar versions dropdown on mobile view"}, "theme.common.skipToMainContent": {"message": "跳到主要内容", "description": "The skip to content label used for accessibility, allowing to rapidly navigate to main content with keyboard tab/enter navigation"}, "theme.tags.tagsListLabel": {"message": "标签：", "description": "The label alongside a tag list"}, "theme.TOCCollapsible.toggleButtonLabel": {"message": "本页总览", "description": "The label used by the button on the collapsible TOC component"}, "theme.CodeBlock.copied": {"message": "复制成功", "description": "The copied button label on code blocks"}, "theme.CodeBlock.copyButtonAriaLabel": {"message": "复制代码到剪贴板", "description": "The ARIA label for copy code blocks button"}, "theme.CodeBlock.copy": {"message": "复制", "description": "The copy button label on code blocks"}, "theme.navbar.mobileLanguageDropdown.label": {"message": "选择语言", "description": "The label for the mobile language switcher dropdown"}, "theme.docs.sidebar.collapseButtonTitle": {"message": "收起侧边栏", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.collapseButtonAriaLabel": {"message": "收起侧边栏", "description": "The title attribute for collapse button of doc sidebar"}, "theme.navbar.mobileSidebarSecondaryMenu.backButtonLabel": {"message": "← 回到主菜单", "description": "The label of the back button to return to main menu, inside the mobile navbar sidebar secondary menu (notably used to display the docs sidebar)"}, "theme.SearchBar.seeAll": {"message": "查看全部 {count} 个结果"}, "theme.SearchBar.label": {"message": "搜索", "description": "The ARIA label and placeholder for search button"}, "theme.SearchPage.documentsFound.plurals": {"message": "找到 {count} 份文件", "description": "Pluralized label for \"{count} documents found\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.SearchPage.existingResultsTitle": {"message": "「{query}」的搜索结果", "description": "The search page title for non-empty query"}, "theme.SearchPage.emptyResultsTitle": {"message": "在文档中搜索", "description": "The search page title for empty query"}, "theme.SearchPage.inputPlaceholder": {"message": "在此输入搜索字词", "description": "The placeholder for search page input"}, "theme.SearchPage.inputLabel": {"message": "搜索", "description": "The ARIA label for search page input"}, "theme.SearchPage.algoliaLabel": {"message": "通过 Algolia 搜索", "description": "The ARIA label for Algolia mention"}, "theme.SearchPage.noResultsText": {"message": "未找到任何结果", "description": "The paragraph for empty search result"}, "theme.SearchPage.fetchingNewResults": {"message": "正在获取新的搜索结果...", "description": "The paragraph for fetching new search results"}, "theme.IdealImageMessage.loading": {"message": "加载中……", "description": "When the full-scale image is loading"}, "theme.IdealImageMessage.load": {"message": "点击加载图片{sizeMessage}", "description": "To prompt users to load the full image. sizeMessage is a parenthesized size figure."}, "theme.IdealImageMessage.offline": {"message": "你的浏览器处于离线状态。图片未加载", "description": "When the user is viewing an offline document"}, "theme.IdealImageMessage.404error": {"message": "未找到图片", "description": "When the image is not found"}, "theme.IdealImageMessage.error": {"message": "出现错误，点击重试", "description": "When the image fails to load for unknown error"}, "theme.PwaReloadPopup.info": {"message": "有可用的新版本", "description": "The text for PWA reload popup"}, "theme.PwaReloadPopup.refreshButtonText": {"message": "刷新", "description": "The text for PWA reload button"}, "theme.PwaReloadPopup.closeButtonAriaLabel": {"message": "关闭", "description": "The ARIA label for close button of PWA reload popup"}, "theme.Playground.result": {"message": "结果", "description": "The result label of the live codeblocks"}, "theme.Playground.liveEditor": {"message": "实时编辑器", "description": "The live editor label of the live codeblocks"}, "showcase.card.sourceLink": {"message": "源码", "description": "The label for the source link of a showcase card"}}