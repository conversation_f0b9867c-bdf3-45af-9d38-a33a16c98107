{"extends": "@docusaurus/tsconfig", "compilerOptions": {"lib": ["DOM", "ESNext"], "baseUrl": ".", "resolveJsonModule": true, "allowUnreachableCode": false, "exactOptionalPropertyTypes": false, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": true, "strict": true, "alwaysStrict": true, "noImplicitAny": false, "noImplicitThis": true, "strictBindCallApply": true, "strictFunctionTypes": true, "strictNullChecks": true, "strictPropertyInitialization": true, "useUnknownInCatchVariables": true, "noUnusedLocals": false, "noUnusedParameters": false, "importsNotUsedAsValues": "remove", "moduleResolution": "<PERSON><PERSON><PERSON>", "skipLibCheck": false}, "exclude": ["src/sw.js"]}