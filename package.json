{"name": "blog", "version": "3.0.0", "author": {"url": "https://github.com/kuizuo", "email": "<EMAIL>", "name": "愧怍"}, "repository": {"url": "https://github.com/kuizuo/blog", "type": "git"}, "homepage": "https://kuizuo.me", "license": "MIT", "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "start:en": "docusaurus start --locale en", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear && rimraf changelog", "serve": "docusaurus serve", "lint": "eslint .", "lint:fix": "eslint . --fix", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "index": "docker run -it --env-file=.env -e \"CONFIG=$(cat docsearch.json | jq -r tostring)\" algolia/docsearch-scraper"}, "dependencies": {"@docusaurus/core": "3.8.1", "@docusaurus/faster": "^3.8.1", "@docusaurus/plugin-ideal-image": "3.8.1", "@docusaurus/plugin-pwa": "3.8.1", "@docusaurus/plugin-vercel-analytics": "^3.8.1", "@docusaurus/preset-classic": "3.8.1", "@docusaurus/theme-search-algolia": "3.8.1", "@giscus/react": "^3.1.0", "@popperjs/core": "^2.11.8", "@radix-ui/react-slot": "^1.1.1", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "docusaurus-plugin-baidu-tongji": "0.0.0-beta.4", "docusaurus-plugin-image-zoom": "^2.0.0", "mini-svg-data-uri": "^1.4.4", "motion": "^11.18.1", "ora": "^8.1.1", "postcss": "^8.5.1", "prism-react-renderer": "^2.4.1", "raw-loader": "^4.0.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-github-calendar": "^4.5.4", "react-icon-cloud": "^4.1.7", "react-popper": "^2.3.0", "react-tweet": "^3.2.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.8.1", "@docusaurus/tsconfig": "^3.8.1", "@docusaurus/types": "3.8.1", "@iconify/react": "^5.2.0", "@stylistic/eslint-plugin": "2.13.0", "@tailwindcss/typography": "^0.5.16", "@typescript-eslint/eslint-plugin": "8.20.0", "eslint": "9.18.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-tailwindcss": "3.18.0", "typescript": "~5.7.3", "typescript-eslint": "^8.20.0"}, "engines": {"node": ">=20.0"}, "packageManager": "pnpm@9.15.4"}